<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG API 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input,
        select,
        textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }

        button:hover {
            background: #0056b3;
        }

        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .response-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            min-height: 200px;
            white-space: pre-wrap;
            font-family: monospace;
        }

        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>

<body>
    <h1>RAG API 测试页面</h1>

    <div class="test-container">
        <h2>测试配置</h2>
        <div class="form-group">
            <label for="apiUrl">API 基础URL:</label>
            <input type="text" id="apiUrl" value="http://localhost:8080" placeholder="http://localhost:8080">
        </div>
        <div class="form-group">
            <label for="memoryId">会话ID (Memory ID):</label>
            <input type="text" id="memoryId" value="12345" placeholder="输入数字或留空自动生成">
        </div>
        <div class="form-group">
            <label for="grade">年级:</label>
            <select id="grade">
                <option value="">请选择年级</option>
                <option value="小学一年级">小学一年级</option>
                <option value="小学二年级">小学二年级</option>
                <option value="小学三年级">小学三年级</option>
                <option value="小学四年级">小学四年级</option>
                <option value="小学五年级">小学五年级</option>
                <option value="小学六年级">小学六年级</option>
                <option value="初中一年级">初中一年级</option>
                <option value="初中二年级">初中二年级</option>
                <option value="初中三年级">初中三年级</option>
                <option value="高中一年级">高中一年级</option>
                <option value="高中二年级">高中二年级</option>
                <option value="高中三年级">高中三年级</option>
            </select>
        </div>
        <div class="form-group">
            <label for="subject">科目:</label>
            <select id="subject">
                <option value="">请选择科目</option>
                <option value="数学">数学</option>
                <option value="物理">物理</option>
                <option value="化学">化学</option>
                <option value="生物">生物</option>
                <option value="语文">语文</option>
                <option value="英语">英语</option>
                <option value="历史">历史</option>
                <option value="地理">地理</option>
                <option value="政治">政治</option>
                <option value="科学">科学</option>
            </select>
        </div>
        <div class="form-group">
            <label for="message">测试消息:</label>
            <textarea id="message" rows="3" placeholder="输入要测试的消息内容">什么是加法？</textarea>
        </div>
        <button onclick="testRagApi()">测试 RAG API</button>
        <button onclick="clearResponse()">清空响应</button>
    </div>

    <div class="test-container">
        <h2>测试结果</h2>
        <div id="status" class="status info">准备就绪，点击"测试 RAG API"开始测试</div>
        <div id="response" class="response-area"></div>
    </div>

    <script>
        let currentEventSource = null;

        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function appendResponse(text) {
            const responseEl = document.getElementById('response');
            responseEl.textContent += text;
            responseEl.scrollTop = responseEl.scrollHeight;
        }

        function clearResponse() {
            document.getElementById('response').textContent = '';
            updateStatus('响应已清空', 'info');
        }

        function testRagApi() {
            // 关闭之前的连接
            if (currentEventSource) {
                currentEventSource.close();
                currentEventSource = null;
            }

            const apiUrl = document.getElementById('apiUrl').value.trim();
            const memoryId = document.getElementById('memoryId').value.trim();
            const grade = document.getElementById('grade').value;
            const subject = document.getElementById('subject').value;
            const message = document.getElementById('message').value.trim();

            if (!apiUrl) {
                updateStatus('请输入API基础URL', 'error');
                return;
            }

            if (!message) {
                updateStatus('请输入测试消息', 'error');
                return;
            }

            // 清空响应区域
            clearResponse();
            updateStatus('正在连接RAG API...', 'info');

            // 构建完整的消息，包含年级和科目信息
            const enhancedMessage = grade && subject
                ? `[${grade} ${subject}] ${message}`
                : message;

            // 构建URL参数
            const params = new URLSearchParams({
                memoryId: memoryId || Date.now().toString(),
                message: enhancedMessage
            });

            const fullUrl = `${apiUrl}/ai/rag/chat-stream?${params.toString()}`;
            console.log('测试RAG API URL:', fullUrl);

            appendResponse(`🔗 连接URL: ${fullUrl}\n`);
            appendResponse(`📝 发送消息: ${enhancedMessage}\n`);
            appendResponse(`⏳ 等待响应...\n\n`);

            try {
                // 创建 EventSource 连接
                currentEventSource = new EventSource(fullUrl);

                // 处理接收到的消息
                currentEventSource.onmessage = function (event) {
                    const data = event.data;
                    if (data && data.trim() !== '') {
                        appendResponse(`🤖 AI回复: ${data}\n`);
                    }
                };

                // 处理错误
                currentEventSource.onerror = function (error) {
                    console.error('RAG SSE 连接错误:', error);
                    if (currentEventSource.readyState !== EventSource.CLOSED) {
                        updateStatus('连接错误，请检查后端服务是否运行', 'error');
                        appendResponse(`❌ 连接错误: ${error.message || '未知错误'}\n`);
                        currentEventSource.close();
                    }
                };

                // 处理连接关闭
                currentEventSource.addEventListener('close', function () {
                    console.log('RAG SSE 连接已关闭');
                    updateStatus('连接已关闭', 'info');
                    appendResponse(`🔒 连接已关闭\n`);
                    currentEventSource = null;
                });

                updateStatus('连接成功，等待AI响应...', 'success');

            } catch (error) {
                console.error('创建EventSource失败:', error);
                updateStatus(`创建连接失败: ${error.message}`, 'error');
                appendResponse(`❌ 创建连接失败: ${error.message}\n`);
            }
        }

        // 页面卸载时清理连接
        window.addEventListener('beforeunload', function () {
            if (currentEventSource) {
                currentEventSource.close();
            }
        });

        // 自动填充一些测试数据
        document.addEventListener('DOMContentLoaded', function () {
            document.getElementById('grade').value = '小学三年级';
            document.getElementById('subject').value = '数学';
        });
    </script>
</body>

</html>