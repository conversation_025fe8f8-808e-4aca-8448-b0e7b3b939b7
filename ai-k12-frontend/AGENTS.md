# Repository Guidelines

## Project Structure & Module Organization
- Root: `index.html`, Vite config (`vite.config.js`), npm files.
- Source: `src/`
  - `api/`: HTTP clients (e.g., `chatApi.js`, `ragApi.js`, `speechApi.js`).
  - `components/`: Vue SFCs (UI, chat, selectors, tests).
  - `router/`: Route definitions (`index.js`).
  - `config/`: Runtime config and env helpers (`env.js`).
  - `utils/`: Reusable helpers (audio, streaming, parsing).
  - `assets/`: Static images used by components.
- Public assets: `public/`
- Build output: `dist/`
- Ad‑hoc demos: `api-test.html`, `record-wav-test.html`, `test-rag-api.html`, `testsound.html`.

## Build, Test, and Development Commands
- `npm run dev`: Start Vite dev server on `http://localhost:3000`.
- `npm run build`: Production build into `dist/` (terser enabled, vendor chunk split).
- `npm run preview`: Preview the production build locally.
- Manual demos: open the HTML demo files in a browser or serve the repo root during development (after `npm run dev` or with a static server) to exercise APIs/audio.

## Coding Style & Naming Conventions
- Indentation: 2 spaces; ES modules throughout.
- Vue components: `PascalCase.vue` with `name` matching filename.
- JS modules, functions, variables: `camelCase`; constants: `SCREAMING_SNAKE_CASE`.
- API modules: end with `Api.js` (e.g., `ragApi.js`).
- Environment vars: prefix with `VITE_` (client-exposed), configured via `.env` files.
- Formatting/linting: follow existing style; keep imports ordered and component templates concise.

## Testing Guidelines
- No unit test framework is configured. Use the HTML demos for manual checks:
  - API streaming: `api-test.html`, `test-rag-api.html`.
  - Audio/recording: `record-wav-test.html`, `testsound.html`.
- Validate routes via `src/router/index.js` and smoke test UI flows in `npm run dev` and `npm run preview`.

## Commit & Pull Request Guidelines
- Commits: imperative mood, concise subject, optional scope (e.g., `feat(chat): stream SSE chunks`).
- Include context in body when changing behavior or APIs.
- PRs: clear description, linked issues, screenshots or clips for UI changes, and steps to reproduce/verify. Keep diffs focused.

## Security & Configuration Tips
- Do not commit secrets. Only `VITE_` vars are exposed to the client at runtime (e.g., `VITE_API_URL`).
- Backend endpoints used: `/chat/k12/stream`, `/rag/k12/stream`, `/speech/stt`, `/speech/tts`.
- CORS is enabled in dev; confirm production domains in your backend.
