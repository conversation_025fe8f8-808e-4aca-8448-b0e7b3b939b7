# AI K12 Frontend RAG 集成说明

## 改造完成内容

### 1. RAG API 集成
- 修正了 `src/api/ragApi.js` 中的接口路径，现在正确调用后端 `/ai/rag/chat-stream` 接口
- 将年级和科目信息集成到 RAG 请求中，格式：`[年级 科目] 用户消息`
- 实现了流式响应处理，支持实时显示 AI 回复

### 2. 聊天组件升级
- 更新了 `ChatWindow.vue` 组件，移除模拟响应，接入真实 RAG API
- 添加了流式响应可视化指示器（动态点点点动画）
- 集成了年级和科目选择到聊天请求中
- 添加了加载状态管理和错误处理

### 3. 用户体验优化
- 聊天过程中显示实时输入动画
- 发送按钮在加载时自动禁用
- 组件销毁时自动清理 SSE 连接
- 消息自动滚动到底部

### 4. 功能简化（2025-08-24）
- 移除了分屏学习模式功能，简化用户界面
- 聊天功能保持在主界面，提供更流畅的对话体验
- 保留了年级和科目选择功能，确保 RAG 请求的准确性

## 使用流程

1. **选择年级和科目**：用户在界面上选择对应的年级（如"小学三年级"）和科目（如"数学"）

2. **输入问题**：在聊天框中输入学习相关的问题

3. **发送消息**：点击聊天框右侧的箭头按钮

4. **AI 流式回复**：系统会：
   - 将用户的年级和科目信息附加到消息前面
   - 调用后端 `/ai/rag/chat-stream` 接口
   - 实时显示 AI 的流式回复
   - 显示动态输入指示器

## 技术实现细节

### API 调用格式
```javascript
// 用户选择：小学三年级 + 数学 + 问题："什么是加法？"
// 实际发送给后端的消息："[小学三年级 数学] 什么是加法？"

k12RagChatStream(
  memoryId,           // 会话ID
  enhancedMessage,    // 包含年级科目的完整消息
  subject,           // 科目
  grade,             // 年级
  onMessage,         // 流式消息回调
  onError,           // 错误处理回调
  onClose            // 连接关闭回调
)
```

### 后端接口
- **路径**：`GET /ai/rag/chat-stream`
- **参数**：
  - `memoryId`: 会话ID（自动生成）
  - `message`: 包含年级科目信息的完整消息
- **响应**：Server-Sent Events (SSE) 流式响应

## 启动和测试

### 1. 启动后端服务
```bash
cd ai-code-helper-master
mvn spring-boot:run
```

### 2. 启动前端开发服务器
```bash
cd ai-k12-frontend
npm install
npm run dev
```

### 3. 测试步骤
1. 打开浏览器访问 `http://localhost:5173`
2. 选择年级（如：小学三年级）
3. 选择科目（如：数学）
4. 在聊天框输入问题："什么是加法？"
5. 点击发送按钮
6. 观察 AI 的流式回复

## 注意事项

1. **后端服务必须运行**：确保后端服务在 `localhost:8080` 上运行
2. **CORS 配置**：后端需要正确配置 CORS 允许前端访问
3. **SSE 支持**：浏览器必须支持 Server-Sent Events
4. **网络连接**：确保前端能够访问后端 API

## 问题修复记录

### 2025-08-24: 修复 memoryId 类型不匹配问题

**问题描述：**
```
WARN: Method parameter 'memoryId': Failed to convert value of type 'java.lang.String' to required type 'int'
```

**原因分析：**
- 后端接口 `@RequestParam int memoryId` 期望 int 类型
- 前端传递的是字符串类型的时间戳（如 "1755998149164"）
- Spring Boot 无法自动转换字符串到 int 类型

**解决方案：**
1. **后端修改**：将 `AiController.ragChatStream` 方法的 `memoryId` 参数类型改为 `String`
2. **类型转换**：在方法内部将字符串转换为 int，如果转换失败则使用 hashCode 作为替代
3. **前端优化**：确保传递的 memoryId 是有效的数字字符串

**修改详情：**
```java
// 修改前
@GetMapping("/rag/chat-stream")
public Flux<ServerSentEvent<String>> ragChatStream(@RequestParam int memoryId, @RequestParam String message)

// 修改后  
@GetMapping("/rag/chat-stream")
public Flux<ServerSentEvent<String>> ragChatStream(@RequestParam String memoryId, @RequestParam String message) {
    // 将字符串memoryId转换为int
    int memoryIdInt;
    try {
        memoryIdInt = Integer.parseInt(memoryId);
    } catch (NumberFormatException e) {
        memoryIdInt = Math.abs(memoryId.hashCode());
        log.warn("无法解析memoryId为int: {}, 使用hashCode: {}", memoryId, memoryIdInt);
    }
    
    return aiCodeHelperService.chatStream(memoryIdInt, message.trim())
    // ... 其他代码
}
```

**测试验证：**
- 创建了 `test-rag-api.html` 测试页面
- 可以独立测试 RAG API 接口
- 支持年级、科目选择和消息输入
- 实时显示流式响应结果

## 故障排除

如果遇到问题，请检查：

1. **控制台错误**：打开浏览器开发者工具查看错误信息
2. **网络请求**：检查网络面板中的 API 请求状态
3. **后端日志**：查看后端服务的日志输出
4. **API 配置**：确认 `src/config/env.js` 中的 API URL 正确

## API 环境配置

默认配置在 `src/config/env.js`：
- 开发环境：`http://localhost:8080`
- 生产环境：使用当前域名

可通过环境变量 `VITE_API_URL` 覆盖默认配置。
