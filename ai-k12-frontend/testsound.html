<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>语音输入小助手</title>
    <style>
        body { font-family: sans-serif; display: flex; flex-direction: column; align-items: center; padding-top: 50px; }
        #recordButton {
            width: 100px; height: 100px;
            border-radius: 50%;
            background-color: #4CAF50;
            border: none;
            color: white;
            font-size: 50px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.1s;
        }
        #recordButton:active {
            background-color: #f44336;
            transform: scale(0.95);
        }
        #status { margin-top: 20px; font-size: 18px; color: #555; }
        #result { margin-top: 20px; font-size: 24px; font-weight: bold; min-height: 30px; }
    </style>
</head>
<body>
    <button id="recordButton">🎙️</button>
    <p id="status">按住按钮，开始说话</p>
    <div id="result"></div>

    <script>
        const recordButton = document.getElementById('recordButton');
        const statusDisplay = document.getElementById('status');
        const resultDisplay = document.getElementById('result');
        const API_ENDPOINT = "http://127.0.0.1:8000/transcribe"; // 你的后端API地址

        let mediaRecorder;
        let audioChunks = [];

        // 按下按钮
        recordButton.addEventListener('mousedown', async () => {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                mediaRecorder = new MediaRecorder(stream);
                
                mediaRecorder.ondataavailable = event => {
                    audioChunks.push(event.data);
                };

                mediaRecorder.onstop = sendAudio;

                audioChunks = [];
                mediaRecorder.start();
                statusDisplay.textContent = "正在听你说话... 松开结束";
                resultDisplay.textContent = "";
            } catch (err) {
                console.error("无法获取麦克风:", err);
                statusDisplay.textContent = "无法使用麦克风哦！";
            }
        });

        // 松开按钮
        recordButton.addEventListener('mouseup', () => {
            if (mediaRecorder && mediaRecorder.state === "recording") {
                mediaRecorder.stop();
                statusDisplay.textContent = "正在努力识别...";
            }
        });

        // 发送音频到后端
        async function sendAudio() {
            const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
            const formData = new FormData();
            formData.append('audio_file', audioBlob, 'recording.webm');

            try {
                const response = await fetch(API_ENDPOINT, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`服务器错误: ${response.statusText}`);
                }

                const data = await response.json();
                resultDisplay.textContent = data.text;
                statusDisplay.textContent = "识别完成！按住按钮继续说话";
            } catch (error) {
                console.error('识别失败:', error);
                statusDisplay.textContent = "哎呀，没听清，再试一次吧！";
            }
        }
    </script>
</body>
</html>