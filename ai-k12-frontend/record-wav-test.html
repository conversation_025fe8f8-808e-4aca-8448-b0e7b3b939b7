<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <title>WAV 录音本地保存测试</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif; padding: 24px; line-height: 1.6; }
    h1 { margin-top: 0; }
    .row { margin: 12px 0; }
    button { padding: 8px 14px; margin-right: 8px; }
    .meter { height: 10px; background: #eee; border-radius: 6px; overflow: hidden; width: 280px; display: inline-block; vertical-align: middle; }
    .bar { height: 100%; background: #4caf50; width: 0%; transition: width 80ms linear; }
    .mono { font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; }
    audio { display: block; margin-top: 8px; }
    .hint { color: #666; font-size: 14px; }
  </style>
  <!-- 注意：大多数浏览器要求在 HTTPS 或 localhost 环境下才能使用麦克风 -->
</head>
<body>
  <h1>WAV 录音本地保存测试</h1>
  <div class="row hint">在 localhost 或 HTTPS 打开本页，点击开始录音，说话 3–5 秒后停止，下载 WAV 并试听。</div>

  <div class="row">
    <label>麦克风设备：</label>
    <select id="device"></select>
    <button id="btnRefresh">刷新</button>
  </div>
  <div class="row">
    <label><input type="checkbox" id="ec" checked> 回声消除</label>
    <label><input type="checkbox" id="ns" checked> 噪声抑制</label>
    <label><input type="checkbox" id="agc" checked> 自动增益</label>
  </div>
  <div class="row">
    <button id="btnStart">开始录音</button>
    <button id="btnStop" disabled>停止录音</button>
    <button id="btnReset">重置</button>
    <button id="btnMonitor" disabled>本地监听</button>
  </div>

  <div class="row">
    <strong>状态：</strong><span id="status">待机</span>
  </div>
  <div class="row">
    <strong>时长：</strong><span id="dur">0.0s</span>
    &nbsp;&nbsp;
    <strong>输入采样率：</strong><span id="sr">-</span> Hz
    &nbsp;&nbsp;
    <strong>WAV大小：</strong><span id="size">-</span>
  </div>
  <div class="row">
    <div class="meter" title="输入音量 (RMS)"><div class="bar" id="bar"></div></div>
    &nbsp;
    <span class="hint">若说话时条不动，说明采集不到声音</span>
  </div>

  <div class="row" id="downloadRow" style="display:none">
    <a id="download" download="recording.wav">⬇️ 下载 WAV</a>
    <audio id="player" controls></audio>
  </div>

  <script>
    // 简单的 Float32 PCM -> 16-bit PCM WAV 编码器
    function downsampleBuffer(buffer, inSampleRate, outSampleRate) {
      if (outSampleRate >= inSampleRate) return buffer;
      const ratio = inSampleRate / outSampleRate;
      const newLen = Math.floor(buffer.length / ratio);
      const result = new Float32Array(newLen);
      let offsetResult = 0, offsetBuffer = 0;
      while (offsetResult < newLen) {
        const nextOffsetBuffer = Math.round((offsetResult + 1) * ratio);
        let accum = 0, count = 0;
        for (let i = offsetBuffer; i < nextOffsetBuffer && i < buffer.length; i++) {
          accum += buffer[i]; count++;
        }
        result[offsetResult++] = accum / (count || 1);
        offsetBuffer = nextOffsetBuffer;
      }
      return result;
    }
    function floatTo16BitPCM(float32Array) {
      const buffer = new ArrayBuffer(float32Array.length * 2);
      const view = new DataView(buffer);
      let offset = 0;
      for (let i = 0; i < float32Array.length; i++, offset += 2) {
        let s = Math.max(-1, Math.min(1, float32Array[i]));
        view.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7FFF, true);
      }
      return view;
    }
    function encodeWAV(float32Array, inSampleRate, outSampleRate = 16000) {
      const mono = downsampleBuffer(float32Array, inSampleRate, outSampleRate);
      const pcm16 = floatTo16BitPCM(mono);
      const headerSize = 44;
      const dataSize = pcm16.byteLength;
      const buffer = new ArrayBuffer(headerSize + dataSize);
      const view = new DataView(buffer);
      writeString(view, 0, 'RIFF');
      view.setUint32(4, 36 + dataSize, true);
      writeString(view, 8, 'WAVE');
      writeString(view, 12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true);      // PCM
      view.setUint16(22, 1, true);      // mono
      view.setUint32(24, outSampleRate, true);
      view.setUint32(28, outSampleRate * 2, true); // byte rate
      view.setUint16(32, 2, true);      // block align
      view.setUint16(34, 16, true);     // bits per sample
      writeString(view, 36, 'data');
      view.setUint32(40, dataSize, true);
      new Uint8Array(buffer, headerSize).set(new Uint8Array(pcm16.buffer));
      return new Uint8Array(buffer);
    }
    function writeString(view, offset, string) { for (let i = 0; i < string.length; i++) view.setUint8(offset + i, string.charCodeAt(i)); }

    const btnStart = document.getElementById('btnStart');
    const btnStop  = document.getElementById('btnStop');
    const btnReset = document.getElementById('btnReset');
    const btnRefresh = document.getElementById('btnRefresh');
    const btnMonitor = document.getElementById('btnMonitor');
    const $status  = document.getElementById('status');
    const $dur     = document.getElementById('dur');
    const $sr      = document.getElementById('sr');
    const $size    = document.getElementById('size');
    const $bar     = document.getElementById('bar');
    const $row     = document.getElementById('downloadRow');
    const $dl      = document.getElementById('download');
    const $player  = document.getElementById('player');
    const $device  = document.getElementById('device');
    const $ec      = document.getElementById('ec');
    const $ns      = document.getElementById('ns');
    const $agc     = document.getElementById('agc');

    let mediaStream, audioContext, source, processor;
    let chunks = []; // Float32Array[]
    let startAt = 0; let timer = 0; let inSampleRate = 0;
    let monitorOn = false; let monitorEl;

    function setStatus(s) { $status.textContent = s; }
    function setDur() {
      if (!startAt) { $dur.textContent = '0.0s'; return; }
      const secs = ((Date.now() - startAt) / 1000).toFixed(1);
      $dur.textContent = secs + 's';
    }
    function meterRMS(data) {
      let sum = 0; for (let i = 0; i < data.length; i++) sum += data[i] * data[i];
      const rms = Math.sqrt(sum / data.length);
      const pct = Math.min(100, Math.max(0, rms * 200)); // 粗略缩放
      $bar.style.width = pct + '%';
    }

    async function enumerate() {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const inputs = devices.filter(d => d.kind === 'audioinput');
        $device.innerHTML = '';
        for (const d of inputs) {
          const opt = document.createElement('option');
          opt.value = d.deviceId; opt.textContent = d.label || `麦克风(${d.deviceId.slice(0,6)})`;
          $device.appendChild(opt);
        }
      } catch (e) { console.warn('enumerateDevices 失败:', e); }
    }
    btnRefresh.onclick = enumerate;

    btnStart.onclick = async () => {
      try {
        btnStart.disabled = true; btnStop.disabled = false; btnMonitor.disabled = false; $row.style.display = 'none';
        setStatus('请求麦克风权限...');
        const constraints = { audio: {
          channelCount: 1,
          noiseSuppression: $ns.checked,
          echoCancellation: $ec.checked,
          autoGainControl: $agc.checked,
        } };
        if ($device.value) constraints.audio.deviceId = { exact: $device.value };
        mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
        setStatus('录音中...');
        audioContext = new (window.AudioContext || window.webkitAudioContext)({ latencyHint: 'interactive' });
        inSampleRate = audioContext.sampleRate; $sr.textContent = String(inSampleRate);
        source = audioContext.createMediaStreamSource(mediaStream);
        processor = audioContext.createScriptProcessor(4096, 1, 1);
        processor.onaudioprocess = e => {
          const data = e.inputBuffer.getChannelData(0);
          chunks.push(new Float32Array(data));
          meterRMS(data);
        };
        source.connect(processor); processor.connect(audioContext.destination);
        chunks = []; startAt = Date.now(); timer = setInterval(setDur, 200);
      } catch (e) {
        setStatus('无法使用麦克风：' + e.message); btnStart.disabled = false; btnStop.disabled = true; btnMonitor.disabled = true;
      }
    };

    btnStop.onclick = async () => {
      try {
        btnStop.disabled = true; btnMonitor.disabled = true; clearInterval(timer); setDur(); setStatus('处理中...');
        if (processor) processor.disconnect(); if (source) source.disconnect();
        if (audioContext && audioContext.state !== 'closed') await audioContext.close();
        if (mediaStream) mediaStream.getTracks().forEach(t => t.stop());

        // 拼接 Float32 并编码 WAV（16k/16bit/mono）
        let total = 0; for (const c of chunks) total += c.length;
        const float32 = new Float32Array(total); let off = 0;
        for (const c of chunks) { float32.set(c, off); off += c.length; }
        const wavBytes = encodeWAV(float32, inSampleRate, 16000);
        const blob = new Blob([wavBytes], { type: 'audio/wav' });
        const url = URL.createObjectURL(blob);
        $size.textContent = (blob.size / 1024).toFixed(1) + ' KB';
        $dl.href = url; $player.src = url; $row.style.display = '';
        setStatus('完成，可下载并试听。');
      } catch (e) {
        setStatus('处理失败：' + e.message);
      } finally {
        btnStart.disabled = false;
      }
    };

    btnReset.onclick = () => {
      if (mediaStream) mediaStream.getTracks().forEach(t => t.stop());
      if (audioContext && audioContext.state !== 'closed') audioContext.close();
      chunks = []; startAt = 0; clearInterval(timer);
      $bar.style.width = '0%'; $dur.textContent = '0.0s'; $size.textContent = '-'; $row.style.display = 'none';
      setStatus('已重置'); btnStart.disabled = false; btnStop.disabled = true; btnMonitor.disabled = true;
    };

    btnMonitor.onclick = () => {
      try {
        if (!mediaStream) return;
        if (!monitorEl) {
          monitorEl = document.createElement('audio');
          monitorEl.controls = true; monitorEl.autoplay = true; monitorEl.style.display = 'block';
          document.body.appendChild(monitorEl);
        }
        monitorEl.srcObject = mediaStream;
      } catch {}
    };

    // 初始化设备列表
    if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
      enumerate();
    }
  </script>
</body>
</html>
