# Thinky - AI K12教育助手

一个基于RAG技术的K12教育AI助手前端应用，支持多模态交互（文字、图片、语音）和分屏学习模式。

## ✨ 功能特性

- 🎯 **多科目支持**: 数学、物理、化学、生物、语文、英语等K12全科目
- 📚 **分年级学习**: 小学一年级到高中三年级的个性化学习内容
- 💬 **多模态交互**: 支持文字、图片、语音多种输入方式
- 🖥️ **分屏学习模式**: 左侧输入，右侧AI输出的专业学习界面
- 🧠 **RAG知识库**: 基于教育专业知识库的智能问答
- 🎤 **语音功能**: 语音识别、语音合成、语音评测
- 📱 **响应式设计**: 完美适配桌面端和移动端

## 🎨 界面设计

基于Figma设计稿100%还原，包含：
- 现代化的左侧导航栏
- 直观的科目选择界面
- 便捷的年级选择下拉菜单
- 功能丰富的聊天窗口
- 专业的分屏学习模式

## 🛠️ 技术栈

- **框架**: Vue 3 + Composition API
- **构建工具**: Vite 4
- **HTTP客户端**: Axios
- **Markdown渲染**: Marked
- **样式**: CSS3 + Flexbox + Grid
- **模块化**: ES6 Modules

## 📦 项目结构

```
ai-k12-frontend/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口
│   │   ├── chatApi.js     # 聊天API
│   │   ├── ragApi.js      # RAG知识库API
│   │   └── speechApi.js   # 语音API
│   ├── assets/            # 资源文件
│   │   └── images/        # 图片资源
│   ├── components/        # Vue组件
│   │   ├── Sidebar.vue    # 侧边栏
│   │   ├── SubjectSelector.vue  # 科目选择
│   │   ├── GradeSelector.vue    # 年级选择
│   │   ├── ChatWindow.vue       # 聊天窗口
│   │   └── StudyMode.vue        # 学习模式
│   ├── config/            # 配置文件
│   │   └── env.js         # 环境配置
│   ├── utils/             # 工具函数
│   │   └── index.js       # 通用工具
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── .env                   # 环境变量
├── .env.production        # 生产环境变量
├── index.html             # HTML模板
├── package.json           # 依赖配置
├── vite.config.js         # Vite配置
└── README.md             # 项目文档
```

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
cd ai-k12-frontend
npm install
```

### 启动开发服务器

```bash
npm run dev
```

访问 http://localhost:3000 查看应用

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## ⚙️ 配置说明

### 环境变量

在 `.env` 文件中配置：

```env
# API后端地址
VITE_API_URL=http://localhost:8080

# 应用标题
VITE_APP_TITLE=Thinky - AI K12教育助手

# 功能开关
VITE_ENABLE_SPEECH=true
VITE_ENABLE_IMAGE_UPLOAD=true
VITE_ENABLE_VOICE_INPUT=true
```

### API接口

确保后端服务提供以下接口：

- `/chat/k12/stream` - K12聊天流式接口
- `/rag/k12/stream` - RAG知识库流式接口
- `/speech/stt` - 语音识别接口
- `/speech/tts` - 语音合成接口

## 📱 使用指南

### 基本使用

1. **选择科目**: 在主界面点击科目图标选择学习科目
2. **选择年级**: 点击"Choose Grade"下拉菜单选择年级
3. **开始对话**: 在聊天窗口输入问题或上传图片
4. **分屏模式**: 有问题输入后自动切换到分屏学习模式

### 高级功能

- **语音输入**: 点击麦克风图标进行语音输入
- **图片上传**: 点击相机图标上传题目图片
- **学习模式**: 支持文字、图片、语音三种输入方式
- **知识扩展**: 查看相关知识点、练习题和学习技巧

## 🔧 开发指南

### 添加新科目

在 `src/config/env.js` 的 `subjects` 数组中添加：

```javascript
{
  id: 'new_subject',
  name: '新科目',
  icon: '📖'
}
```

### 添加新年级

在 `src/config/env.js` 的 `grades` 数组中添加：

```javascript
{
  id: 'new_grade',
  name: '新年级',
  level: 'category'
}
```

### 自定义主题

修改 `src/config/env.js` 中的 `themes` 配置：

```javascript
themes: {
  custom: {
    primary: '#your-color',
    secondary: '#your-color',
    background: '#your-color',
    surface: '#your-color',
    text: '#your-color'
  }
}
```

## 🐛 常见问题

### 语音功能不工作

1. 检查浏览器是否支持Web Speech API
2. 确保网站使用HTTPS协议
3. 检查麦克风权限设置

### 图片上传失败

1. 检查图片格式是否支持（JPG、PNG、GIF、WebP）
2. 确认图片大小不超过10MB
3. 检查后端API是否正常

### 聊天连接失败

1. 确认后端服务是否启动
2. 检查API地址配置是否正确
3. 查看浏览器网络请求是否有错误

## 📝 更新日志

### v1.0.0 (2024-01-20)

- ✨ 初始版本发布
- 🎯 支持K12全科目教育
- 💬 多模态交互功能
- 🖥️ 分屏学习模式
- 📱 响应式设计

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 👥 团队

- **UI/UX设计**: 基于Figma设计稿
- **前端开发**: Vue 3 + 现代化前端技术栈
- **后端集成**: RESTful API + Server-Sent Events

## 📞 联系我们

- 📧 邮箱: <EMAIL>
- 🌐 网站: https://thinky.com
- 📱 微信: thinky-ai

---

**Thinky - Think First, Answer Later. We think with you.** 🚀
