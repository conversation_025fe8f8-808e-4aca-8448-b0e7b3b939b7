<template>
  <div class="grade-selector">
    <div class="grade-button" @click="toggleDropdown">
      <div class="grade-content">
        <span class="grade-text">{{ selectedGrade || 'Choose Grade' }}</span>
      </div>
      <div class="dropdown-arrow" :class="{ rotated: showDropdown }">
        <svg width="11" height="11" viewBox="0 0 11 11">
          <path d="M3 4L5.5 6.5L8 4" stroke="#A0A4B7" stroke-width="2" fill="none"/>
        </svg>
      </div>
    </div>

    <!-- 下拉菜单 -->
    <div v-if="showDropdown" class="dropdown-menu">
      <div 
        v-for="grade in grades" 
        :key="grade"
        class="dropdown-item"
        :class="{ active: selectedGrade === grade }"
        @click="selectGrade(grade)"
      >
        {{ grade }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GradeSelector',
  props: {
    selectedGrade: {
      type: String,
      default: ''
    }
  },
  emits: ['grade-select'],
  data() {
    return {
      showDropdown: false,
      grades: [
        'Grade 1',
        'Grade 2', 
        'Grade 3',
        'Grade 4',
        'Grade 5',
        'Grade 6',
        'Grade 7',
        'Grade 8',
        'Grade 9',
        'Grade 10',
        'Grade 11',
        'Grade 12'
      ]
    }
  },
  methods: {
    toggleDropdown() {
      this.showDropdown = !this.showDropdown
    },
    
    selectGrade(grade) {
      this.$emit('grade-select', grade)
      this.showDropdown = false
    },
    
    closeDropdown(event) {
      if (!this.$el.contains(event.target)) {
        this.showDropdown = false
      }
    }
  },
  
  mounted() {
    document.addEventListener('click', this.closeDropdown)
  },
  
  beforeUnmount() {
    document.removeEventListener('click', this.closeDropdown)
  }
}
</script>

<style scoped>
.grade-selector {
  position: relative;
  display: inline-block;
}

.grade-button {
  background: #eeeef9;
  border: none;
  border-radius: 16px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
  height: 45px;
  justify-content: center;
}

.grade-button:hover {
  background: #e0e1f7;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(124, 137, 190, 0.2);
}

.grade-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.grade-text {
  font-size: 14px;
  color: #7c89be;
  font-weight: 500;
  line-height: 1.15;
}

.dropdown-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.dropdown-arrow.rotated {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-top: 4px;
  max-height: 200px;
  overflow-y: auto;
  animation: slideDown 0.2s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-item {
  padding: 10px 16px;
  cursor: pointer;
  font-size: 13px;
  color: #333;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
}

.dropdown-item:last-child {
  border-bottom: none;
  border-radius: 0 0 12px 12px;
}

.dropdown-item:first-child {
  border-radius: 12px 12px 0 0;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item.active {
  background-color: #e3f2fd;
  color: #1976d2;
  font-weight: 500;
}

/* 滚动条样式 */
.dropdown-menu::-webkit-scrollbar {
  width: 6px;
}

.dropdown-menu::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grade-button {
    min-width: 140px;
    padding: 10px 16px;
    height: 36px;
  }
  
  .grade-text {
    font-size: 14px;
  }
  
  .dropdown-menu {
    max-height: 160px;
  }
  
  .dropdown-item {
    padding: 12px 16px;
    font-size: 14px;
  }
}
</style>
