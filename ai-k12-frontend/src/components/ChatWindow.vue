<template>
  <div class="chat-window">
    <!-- 聊天容器 -->
    <div class="chat-container">




      <!-- 消息列表 -->
      <div v-if="messages.length > 0" class="messages-list" :class="{ 'expanded': !showInput }" ref="messagesList">
        <div 
          v-for="message in messages" 
          :key="message.id"
          class="message-item"
          :class="{ 'user-message': message.isUser, 'ai-message': !message.isUser }"
        >
          <div class="message-avatar">
            <div v-if="!message.isUser" class="ai-avatar">🤖</div>
            <div v-else class="user-avatar">👤</div>
          </div>
          
          <div class="message-content">
            <div class="message-bubble">
              <!-- 图片消息 -->
              <img v-if="message.image" :src="message.image" alt="Message image" class="message-image" />
              

              
              <!-- 文本消息 -->
              <div v-if="message.text" class="message-text" v-html="formatMessage(message.text)"></div>
              
              <!-- 流式响应指示器 -->
              <div v-if="message.isStreaming" class="streaming-indicator">
                <div class="typing-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
            
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
        </div>
      </div>

      <!-- 聊天输入区域 -->
      <div v-if="showInput" class="chat-input-container">
        <div class="input-wrapper">
          <!-- 图片预览 -->
          <div v-if="selectedImage" class="image-preview">
            <img :src="selectedImage" alt="Selected image" />
            <button class="remove-image" @click="removeImage">×</button>
          </div>

          <!-- 文本输入框 -->
          <textarea
            v-model="inputMessage"
            @keydown="handleKeyDown"
            @input="adjustHeight"
            ref="messageInput"
            :placeholder="placeholderText"
            rows="1"
            class="main-input"
          ></textarea>

          <!-- 功能按钮组 -->
          <div class="input-actions">
            <!-- 图片上传按钮 -->
            <button class="action-btn" @click="selectImage" title="上传图片">
              <svg width="16" height="16" viewBox="0 0 16 16">
                <path d="M14 2H2C1.45 2 1 2.45 1 3v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1V3c0-.55-.45-1-1-1zM2 3h12v6l-3-3c-.28-.28-.72-.28-1 0L6 9l-2-2c-.28-.28-.72-.28-1 0L2 9V3z" fill="currentColor"/>
              </svg>
            </button>
            
            <!-- 相机按钮 -->
            <button class="action-btn" @click="openMobileUpload" title="拍照上传">
              <svg width="16" height="16" viewBox="0 0 16 16">
                <path d="M7 4h2l1-2h2l1 2h2c.55 0 1 .45 1 1v8c0 .55-.45 1-1 1H5c-.55 0-1-.45-1-1V5c0-.55.45-1 1-1zm5 6c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2z" fill="currentColor"/>
              </svg>
            </button>
            
            <!-- 麦克风按钮 -->
            <button 
              class="action-btn voice-btn" 
              @click="toggleVoiceInput" 
              :class="{ 'active': isRecording }"
              title="语音输入"
            >
              <svg width="16" height="16" viewBox="0 0 16 16">
                <path d="M8 1c1.66 0 3 1.34 3 3v4c0 1.66 1.34 3-3 3s-3-1.34-3-3V4c0-1.66 1.34-3 3-3zm5 4v4c0 2.76-2.24 5-5 5s-5-2.24-5-5V5c0-.55.45-1 1-1s1 .45 1 1v4c0 1.66 1.34 3 3 3s3-1.34 3-3V5c0-.55.45-1 1-1s1 .45 1 1z" fill="currentColor"/>
              </svg>
            </button>
          </div>

          <!-- 发送按钮 -->
          <button 
            class="send-btn" 
            @click="sendMessage" 
            :disabled="!canSend || isLoading"
            :class="{ 'can-send': canSend && !isLoading, 'loading': isLoading }"
          >
            <svg width="16" height="16" viewBox="0 0 16 16">
              <path d="M2 8L14 2L10 8L14 14L2 8Z" fill="currentColor"/>
            </svg>
          </button>

          <!-- 清空对话按钮（输入框右侧） -->
          <button 
            v-if="messages.length > 0"
            class="clear-chat-inline-btn" 
            title="清空对话"
            @click="clearAllMessages"
          >
            清空对话
          </button>
        </div>
      </div>

      <!-- 输入框切换按钮 -->
      <div v-if="!showInput" class="input-toggle-container">
        <button class="input-toggle-btn" @click="showInput = true" title="显示输入框">
          <svg width="20" height="20" viewBox="0 0 20 20">
            <path d="M10 2L10 18M10 18L10 2M2 10L18 10" stroke="currentColor" stroke-width="2" fill="none"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input 
      type="file" 
      ref="fileInput" 
      @change="handleFileSelect" 
      accept="image/*" 
      style="display: none"
    />

    <!-- 手机上传二维码弹窗 -->
    <DesktopQrModal 
      :visible="qrModalVisible" 
      :memory-id="memoryId"
      @close="qrModalVisible = false"
      @uploaded="handleMobileUploaded"
    />
  </div>
</template>

<script>
import { marked } from 'marked'
import { k12RagChatStream } from '../api/ragApi.js'
import { multimodalChatStream } from '../api/chatApi.js'
import DesktopQrModal from './mobile/DesktopQrModal.vue'
// import { speechToText } from '../api/speechApi.js' // 改为本地流式识别

export default {
  name: 'ChatWindow',
  components: { DesktopQrModal },
  props: {
    subject: {
      type: String,
      default: ''
    },
    grade: {
      type: String,
      default: ''
    }
  },
  emits: ['send-message', 'messages-changed'],
  data() {
    return {
      inputMessage: '',
      selectedImage: null,  // 图片预览的 base64 URL
      selectedImageFile: null,  // 实际的图片文件对象
      messages: [],
      memoryId: Date.now().toString(), // 生成唯一的会话ID
      currentEventSource: null, // 当前的SSE连接
      isLoading: false, // 加载状态
      // 语音识别相关
      isRecording: false,
      ws: null,
      wsState: 'disconnected',
      audioContext: null,
      workletNode: null,
      sourceNode: null,
      sampleRate: 0,
      language: 'zh',
      floatQueue: [], // Float32 分片队列
      accFloat: null, // 累计缓冲
      frameSize: 0, // 20ms 帧大小（sampleRate/50）
      voiceStatus: '待机',
      qrModalVisible: false,
      // 输入框状态控制
      showInput: true
    }
  },
  computed: {
    canSend() {
      return (this.inputMessage.trim() || this.selectedImage) && !this.isLoading
    },
    
    placeholderText() {
      if (this.subject && this.grade) {
        return `向${this.grade}${this.subject}AI助手提问...`
      }
      return 'Type, snap, or say it — let\'s solve it together.'
    },
    

  },
  methods: {
    showMoreOptions() {
      // 显示更多选项菜单（图片上传、文件上传等）
      this.selectImage()
    },
    openMobileUpload() {
      this.qrModalVisible = true
    },
    
    showHistory() {
      // 显示聊天历史记录
      console.log('显示聊天历史')
      // 这里可以添加显示历史记录的逻辑
    },

    // 语音输入相关方法
    toggleVoiceInput() {
      if (this.isRecording) {
        this.stopVoiceInput()
      } else {
        this.startVoiceInput()
      }
    },

    async startVoiceInput() {
      try {
        this.voiceStatus = '请求音频权限...'
        this.isRecording = true
        // 音频
        const stream = await navigator.mediaDevices.getUserMedia({ 
          audio: { 
            channelCount: 1, 
            echoCancellation: true, 
            noiseSuppression: true, 
            autoGainControl: true 
          } 
        })
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)({ latencyHint: 'interactive' })
        await this.audioContext.audioWorklet.addModule('/worklets/pcm-processor.js')
        this.sampleRate = this.audioContext.sampleRate
        this.frameSize = Math.round(this.sampleRate / 50) // 20ms 帧
        this.sourceNode = this.audioContext.createMediaStreamSource(stream)
        this.workletNode = new AudioWorkletNode(this.audioContext, 'pcm-processor')
        this.workletNode.port.onmessage = (e) => {
          if (!e.data || e.data.type !== 'audio') return
          const float32 = e.data.samples
          // 入队
          this.enqueueFloat(float32)
          this.flushFrames()
        }
        this.sourceNode.connect(this.workletNode)
        this.workletNode.connect(this.audioContext.destination)

        // WS
        await this.openVoiceWS()
        this.voiceStatus = '录音中（流式发送）...'
      } catch (e) {
        console.error(e)
        this.voiceStatus = '启动失败: ' + e.message
        this.isRecording = false
      }
    },

    async openVoiceWS() {
      const url = (location.protocol === 'https:' ? 'wss://' : 'ws://') + (location.hostname + ':8001') + '/ws/stream'
      this.ws = new WebSocket(url)
      this.ws.binaryType = 'arraybuffer'
      this.wsState = 'connecting'
      this.ws.onopen = () => {
        this.wsState = 'connected'
        this.ws.send(JSON.stringify({ event: 'start', sampleRate: this.sampleRate, language: this.language }))
      }
      this.ws.onmessage = (evt) => {
        try {
          const msg = JSON.parse(evt.data)
          if (msg.event === 'ack') {
            this.voiceStatus = '开始流式发送...'
          } else if (msg.event === 'final') {
            const recognizedText = msg.text || ''
            this.voiceStatus = '已收到最终结果'
            // 将识别的文字添加到输入框
            if (recognizedText.trim()) {
              this.inputMessage = recognizedText.trim()
              this.adjustHeight()
            }
            try { this.ws.close() } catch {}
          } else if (msg.event === 'error') {
            this.voiceStatus = '服务错误: ' + msg.message
          }
        } catch (_) {}
      }
      this.ws.onclose = () => { this.wsState = 'disconnected' }
      this.ws.onerror = () => { this.wsState = 'error' }
    },

    enqueueFloat(chunk) {
      if (!this.accFloat || this.accFloat.length === 0) {
        this.accFloat = chunk
      } else {
        const merged = new Float32Array(this.accFloat.length + chunk.length)
        merged.set(this.accFloat, 0)
        merged.set(chunk, this.accFloat.length)
        this.accFloat = merged
      }
    },

    flushFrames() {
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) return
      while (this.accFloat && this.accFloat.length >= this.frameSize) {
        const frame = this.accFloat.subarray(0, this.frameSize)
        const rest = this.accFloat.subarray(this.frameSize)
        // float32 -> int16 小端
        const ab = new ArrayBuffer(frame.length * 2)
        const view = new DataView(ab)
        for (let i = 0; i < frame.length; i++) {
          let s = Math.max(-1, Math.min(1, frame[i]))
          view.setInt16(i * 2, s < 0 ? s * 0x8000 : s * 0x7FFF, true)
        }
        this.ws.send(ab)
        // 余量保留
        const remain = new Float32Array(rest.length)
        remain.set(rest, 0)
        this.accFloat = remain
      }
    },

    stopVoiceInput() {
      // 停止音频采集，但保留 WS 连接等待服务端返回 final
      try {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
          this.ws.send(JSON.stringify({ event: 'end' }))
        }
      } catch {}
      // 关闭本地音频链路
      try { if (this.workletNode) this.workletNode.disconnect() } catch {}
      try { if (this.sourceNode) this.sourceNode.disconnect() } catch {}
      try { if (this.audioContext && this.audioContext.state !== 'closed') this.audioContext.close() } catch {}
      this.workletNode = null
      this.sourceNode = null
      this.audioContext = null
      this.isRecording = false
      this.voiceStatus = '已停止，等待识别结果...'
    },

    cleanupVoice() {
      this.isRecording = false
      try { if (this.workletNode) this.workletNode.disconnect() } catch {}
      try { if (this.sourceNode) this.sourceNode.disconnect() } catch {}
      try { if (this.audioContext && this.audioContext.state !== 'closed') this.audioContext.close() } catch {}
      try { if (this.ws && this.ws.readyState === WebSocket.OPEN) this.ws.close() } catch {}
      this.ws = null
      this.accFloat = null
    },
    
    handleKeyDown(event) {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        this.sendMessage()
      }
    },
    
    adjustHeight() {
      this.$nextTick(() => {
        const textarea = this.$refs.messageInput
        if (textarea) {
          textarea.style.height = 'auto'
          textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
        }
      })
    },
    
    sendMessage() {
      if (!this.canSend || this.isLoading) return
      const raw = this.inputMessage.trim()
      const messageText = raw || (this.selectedImage ? '请解析我上传的图片' : '')
      if (!messageText && !this.selectedImage) return
      
      // 在清空本地状态前，缓存要发送的图片信息
      const imageFileToSend = this.selectedImageFile
      const previewUrl = this.selectedImage
      const imageUrlToSend = (!imageFileToSend && previewUrl && !previewUrl.startsWith('data:')) ? previewUrl : null

      const userMessage = {
        id: Date.now(),
        text: messageText,
        image: this.selectedImage,
        isUser: true,
        timestamp: new Date()
      }
      
      this.messages.push(userMessage)
      
      // 发送给父组件
      this.$emit('send-message', {
        text: userMessage.text,
        image: userMessage.image,
        subject: this.subject,
        grade: this.grade
      })
      
      // 清空输入
      this.inputMessage = ''
      this.selectedImage = null
      this.selectedImageFile = null
      this.resetTextareaHeight()
      
      // 隐藏输入框，最大化聊天对话区域
      this.showInput = false
      
      // 确保消息状态变化事件被触发
      this.$nextTick(() => {
        this.$emit('messages-changed', this.messages.length > 0)
      })
      
      // 调用 RAG API 或多模态 API（支持文件或URL）
      this.sendToRAG(messageText, imageFileToSend, imageUrlToSend)
      
      // 滚动到底部
      this.scrollToBottom()
    },
    handleMobileUploaded(payload) {
      try {
        if (payload && payload.imageUrl) {
          this.selectedImage = payload.imageUrl
          this.qrModalVisible = false
        }
      } catch (_) {}
    },
    
    sendToRAG(message, imageFile = null, imageUrl = null) {
      // 关闭之前的连接
      if (this.currentEventSource) {
        this.currentEventSource.close()
      }
      
      this.isLoading = true
      
      // 创建AI响应消息
      const aiMessage = {
        id: Date.now() + 1,
        text: '',
        isUser: false,
        timestamp: new Date(),
        isStreaming: true
      }
      
      this.messages.push(aiMessage)
      this.scrollToBottom()
      
      // 确保AI消息也被计入消息状态
      this.$nextTick(() => {
        this.$emit('messages-changed', this.messages.length > 0)
      })
      
      // 根据是否有图片（文件或URL）选择多模态API
      if (imageFile || imageUrl) {
        console.log('[ChatWindow] 使用多模态聊天API')
        // 使用多模态聊天API
        this.currentEventSource = multimodalChatStream(
          this.memoryId,
          message,
          imageFile || imageUrl,
          // onMessage 回调
          (chunk) => {
            console.log(`[ChatWindow] 收到多模态消息块: [${chunk}]`)

            // 过滤掉流结束标识，避免显示给用户
            if (chunk === '[DONE]' || chunk.trim() === '[DONE]') {
              console.log('[ChatWindow] 收到流结束标识，忽略显示')
              return
            }

            console.log(`[ChatWindow] 添加内容到消息: [${chunk.substring(0, 50)}...]`)
            aiMessage.text += chunk
            this.$forceUpdate() // 强制更新视图
            this.scrollToBottom()
          },
          // onError 回调
          (error) => {
            console.error('[ChatWindow] 多模态聊天错误:', error)
            console.log(`[ChatWindow] 当前消息内容长度: ${aiMessage.text ? aiMessage.text.length : 0}`)

            // 只有在没有收到任何内容时才显示错误消息
            if (!aiMessage.text || aiMessage.text.trim() === '') {
              console.log('[ChatWindow] 没有收到内容，显示错误消息')
              aiMessage.text = '抱歉，服务暂时不可用，请稍后重试。'
            } else {
              console.log('[ChatWindow] 已收到部分内容，保持现有消息不变')
            }
            aiMessage.isStreaming = false
            this.isLoading = false
            this.$forceUpdate()
            
            // 错误情况下也显示输入框
            setTimeout(() => {
              this.showInput = true
            }, 1000)
          },
          // onClose 回调
          () => {
            console.log('[ChatWindow] 多模态流式响应正常关闭')
            console.log(`[ChatWindow] 最终消息内容长度: ${aiMessage.text ? aiMessage.text.length : 0}`)
            aiMessage.isStreaming = false
            this.isLoading = false
            this.currentEventSource = null
            this.$forceUpdate()
            this.scrollToBottom()
            
            // AI回复完成后，延迟显示输入框
            setTimeout(() => {
              this.showInput = true
            }, 1000)
          }
        )
      } else {
        console.log('[ChatWindow] 使用RAG聊天API')
        // 使用原有的RAG流式API
        this.currentEventSource = k12RagChatStream(
          this.memoryId,
          message,
          this.subject,
          this.grade,
          // onMessage 回调
          (chunk) => {
            console.log(`[ChatWindow] 收到RAG消息块: [${chunk}]`)

            // 过滤掉流结束标识，避免显示给用户
            if (chunk === '[DONE]' || chunk.trim() === '[DONE]') {
              console.log('[ChatWindow] 收到流结束标识，忽略显示')
              return
            }

            console.log(`[ChatWindow] 添加内容到消息: [${chunk.substring(0, 50)}...]`)
            aiMessage.text += chunk
            this.$forceUpdate() // 强制更新视图
            this.scrollToBottom()
          },
          // onError 回调
          (error) => {
            console.error('[ChatWindow] RAG聊天错误:', error)
            console.log(`[ChatWindow] 当前消息内容长度: ${aiMessage.text ? aiMessage.text.length : 0}`)

            // 只有在没有收到任何内容时才显示错误消息
            if (!aiMessage.text || aiMessage.text.trim() === '') {
              console.log('[ChatWindow] 没有收到内容，显示错误消息')
              aiMessage.text = '抱歉，服务暂时不可用，请稍后重试。'
            } else {
              console.log('[ChatWindow] 已收到部分内容，保持现有消息不变')
            }
            aiMessage.isStreaming = false
            this.isLoading = false
            this.$forceUpdate()
            
            // 错误情况下也显示输入框
            setTimeout(() => {
              this.showInput = true
            }, 1000)
          },
          // onClose 回调
          () => {
            console.log('[ChatWindow] RAG流式响应正常关闭')
            console.log(`[ChatWindow] 最终消息内容长度: ${aiMessage.text ? aiMessage.text.length : 0}`)
            aiMessage.isStreaming = false
            this.isLoading = false
            this.currentEventSource = null
            this.$forceUpdate()
            this.scrollToBottom()
            
            // AI回复完成后，延迟显示输入框
            setTimeout(() => {
              this.showInput = true
            }, 1000)
          }
        )
      }
    },
    
    selectImage() {
      this.$refs.fileInput.click()
    },
    
    handleFileSelect(event) {
      const file = event.target.files[0]
      if (file && file.type.startsWith('image/')) {
        // 保存文件对象用于API调用
        this.selectedImageFile = file
        
        // 生成预览URL
        const reader = new FileReader()
        reader.onload = (e) => {
          this.selectedImage = e.target.result
        }
        reader.readAsDataURL(file)
      }
    },
    
    removeImage() {
      this.selectedImage = null
      this.selectedImageFile = null
      this.$refs.fileInput.value = ''
    },
    

    
    formatMessage(text) {
      return marked(text, { breaks: true, gfm: true })
    },
    
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    },
    
    resetTextareaHeight() {
      this.$nextTick(() => {
        const textarea = this.$refs.messageInput
        if (textarea) {
          textarea.style.height = 'auto'
        }
      })
    },
    
    scrollToBottom() {
      this.$nextTick(() => {
        const messagesList = this.$refs.messagesList
        if (messagesList) {
          messagesList.scrollTop = messagesList.scrollHeight
        }
      })
    },
    
    // 手动显示输入框
    showInputField() {
      this.showInput = true
    },
    
    // 清空所有消息
    clearAllMessages() {
      this.messages = []
      this.showInput = true
      // 通知父组件消息状态变化
      this.$emit('messages-changed', false)
    }
  },
  
  mounted() {
    // 确保组件挂载时输入框是显示的
    this.showInput = true
  },
  
  watch: {
    messages: {
      handler(newMessages) {
        // 通知父组件消息数量变化
        this.$emit('messages-changed', newMessages.length > 0)
      },
      immediate: true
    }
  },
  
  beforeUnmount() {
    // 组件销毁时关闭连接
    if (this.currentEventSource) this.currentEventSource.close()
    // 清理语音相关资源
    this.cleanupVoice()
  }
}
</script>

<style scoped>
.chat-window {
  width: 100%;
  max-width: 984px;
  margin: 0 auto;
}

.chat-container {
  position: relative;
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
  min-height: auto;
  overflow: visible;
  display: flex;
  flex-direction: column;
}







@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}



.chat-input-container {
  width: 100%;
  max-width: 600px;
  margin: 20px auto 0;
}

.input-wrapper {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 16px;
  padding: 20px 24px;
  display: flex;
  align-items: flex-end;
  gap: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  min-height: 70px;
  position: relative;
}

.image-preview {
  position: relative;
  flex-shrink: 0;
}

.image-preview img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
}

.remove-image {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background: #ff4444;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-wrapper textarea {
  flex: 1;
  border: none;
  outline: none;
  resize: none;
  font-size: 16px;
  line-height: 1.5;
  font-family: inherit;
  min-height: 24px;
  max-height: 120px;
  color: #333;
}

.input-wrapper textarea::placeholder {
  color: #a4a4a4;
  font-size: 16px;
}

/* 功能按钮组样式 */
.input-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-right: 8px;
}

/* 输入框切换按钮样式 */
.input-toggle-container {
  width: 100%;
  max-width: 600px;
  margin: 20px auto 0;
  text-align: center;
}

.input-toggle-btn {
  width: 60px;
  height: 60px;
  border: none;
  border-radius: 50%;
  background: #007bff;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.input-toggle-btn:hover {
  background: #0056b3;
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: #a4a4a4;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.action-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #666;
}

.action-btn.voice-btn.active {
  background: #ff4444;
  color: white;
  animation: pulse 1s infinite;
}

.send-btn {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: #e0e0e0;
  border: none;
  border-radius: 12px;
  color: #999;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.send-btn.can-send {
  background: #007bff;
  color: white;
}

.send-btn:hover.can-send {
  background: #0056b3;
}

.send-btn.loading {
  background: #6c757d;
  cursor: not-allowed;
  animation: pulse 1s infinite;
}

/* 清空对话（输入框右侧） */
.clear-chat-inline-btn {
  flex-shrink: 0;
  height: 40px;
  padding: 0 14px;
  border: none;
  border-radius: 10px;
  background: #f58147;
  color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(245, 129, 71, 0.25);
}

.clear-chat-inline-btn:hover {
  background: #e06a2e;
  transform: translateY(-1px);
}





.messages-list {
  margin-bottom: 20px;
  overflow-y: auto;
  max-height: 400px;
  padding-right: 8px;
  transition: all 0.3s ease;
}

/* 当输入框隐藏时，消息列表占据更多空间 */
.messages-list.expanded {
  max-height: calc(100vh - 150px);
  margin-bottom: 20px;
}

.message-item {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
  gap: 12px;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
}

.ai-avatar,
.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.ai-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.user-avatar {
  background: #007bff;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

.user-message .message-content {
  align-items: flex-end;
}

.message-bubble {
  background: white;
  padding: 12px 16px;
  border-radius: 18px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-message .message-bubble {
  background: #007bff;
  color: white;
}

.message-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
}



.message-text {
  font-size: 14px;
  line-height: 1.5;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

/* 流式响应指示器 */
.streaming-indicator {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.typing-dots {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: #007bff;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 滚动条样式 */
.messages-list::-webkit-scrollbar {
  width: 6px;
}

.messages-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.messages-list::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-container {
    padding: 16px;
  }
  
  .chat-input-container {
    width: calc(100% - 32px);
    margin: 16px auto 0;
  }
  
  .input-toggle-container {
    width: calc(100% - 32px);
    margin: 16px auto 0;
  }
  
  .messages-list {
    max-height: 300px;
  }
  
  .messages-list.expanded {
    max-height: calc(100vh - 200px);
  }
  
  .message-content {
    max-width: 85%;
  }
}
</style>
