<template>
  <div class="sidebar">
    <div class="sidebar-content">
      <!-- <PERSON><PERSON> -->
      <div class="logo-section">
        <div class="logo">
          <div class="logo-icon">
            <div class="logo-shape shape1"></div>
            <div class="logo-shape shape2"></div>
            <div class="logo-shape shape3"></div>
          </div>
          <span class="logo-text">Thinky</span>
        </div>
      </div>

      <!-- Solve 项目 (激活状态) -->
      <div class="nav-item active" @click="$emit('navigate', 'solve')">
        <div class="nav-icon">
          <svg width="24" height="25" viewBox="0 0 24 25">
            <path d="M12 2.96L17.09 8.05L19.5 8.05V19.05C19.5 19.6 19.1 20.05 18.5 20.05H5.5C4.9 20.05 4.5 19.6 4.5 19.05V8.05H6.91L12 2.96Z" fill="#3F51B5" stroke="#3F51B5" stroke-width="1.5"/>
          </svg>
        </div>
        <span class="nav-text">Solve</span>
      </div>

      <!-- Knowledge Map -->
      <div class="nav-item" @click="$emit('navigate', 'knowledge-map')">
        <div class="nav-icon">
          <svg width="24" height="24" viewBox="0 0 24 24">
            <circle cx="12" cy="6" r="4" fill="#CFD8DC"/>
            <circle cx="6" cy="12" r="2" fill="#3F51B5"/>
            <g>
              <circle cx="18" cy="6" r="1.5" fill="#00BCD4"/>
              <circle cx="12" cy="18" r="1.5" fill="#00BCD4"/>
              <circle cx="6" cy="16" r="1.5" fill="#00BCD4"/>
              <circle cx="18" cy="12" r="1.5" fill="#00BCD4"/>
              <circle cx="15" cy="15" r="1.5" fill="#00BCD4"/>
            </g>
          </svg>
        </div>
        <span class="nav-text">Knowledge Map</span>
      </div>

      <!-- Learning Insight -->
      <div class="nav-item" @click="$emit('navigate', 'learning-insight')">
        <div class="nav-icon">
          <svg width="24" height="24" viewBox="0 0 24 24">
            <path d="M12 2L15 8L22 9L17 14L18 21L12 18L6 21L7 14L2 9L9 8L12 2Z" stroke="#00BCD4" stroke-width="1.5" fill="none"/>
            <circle cx="12" cy="10" r="2" fill="#3F51B5"/>
            <path d="M8 16L16 16" stroke="#3F51B5" stroke-width="1.5" stroke-dasharray="0.6 4"/>
          </svg>
        </div>
        <span class="nav-text">Learning Insight</span>
      </div>

      <!-- Voice Test -->
      <div class="nav-item" @click="$emit('navigate', 'test')">
        <div class="nav-icon">
          <svg width="24" height="24" viewBox="0 0 24 24">
            <path d="M12 2C13.1 2 14 2.9 14 4V12C14 13.1 13.1 14 12 14C10.9 14 10 13.1 10 12V4C10 2.9 10.9 2 12 2Z" fill="#3F51B5"/>
            <path d="M19 10V12C19 15.9 15.9 19 12 19C8.1 19 5 15.9 5 12V10" stroke="#00BCD4" stroke-width="2" fill="none"/>
            <path d="M12 19V22" stroke="#3F51B5" stroke-width="2"/>
            <path d="M8 22H16" stroke="#3F51B5" stroke-width="2"/>
          </svg>
        </div>
        <span class="nav-text">Voice Test</span>
      </div>

      <!-- My Space -->
      <div class="nav-item" @click="$emit('navigate', 'my-space')">
        <div class="nav-icon">
          <svg width="24" height="24" viewBox="0 0 24 24">
            <rect x="3" y="4" width="18" height="12" rx="2" stroke="#00BCD4" stroke-width="1" fill="none"/>
            <rect x="2" y="6" width="20" height="8" rx="1" stroke="#00BCD4" stroke-width="1" fill="none"/>
            <rect x="3" y="13" width="18" height="3" stroke="#3F51B5" stroke-width="1" fill="none"/>
          </svg>
        </div>
        <span class="nav-text">My Space</span>
      </div>

      <!-- Dashboard -->
      <div class="nav-item" @click="$emit('navigate', 'dashboard')">
        <div class="nav-icon">
          <svg width="24" height="24" viewBox="0 0 24 24">
            <rect x="3" y="3" width="18" height="18" fill="#00BCD4"/>
            <circle cx="9" cy="15" r="2" fill="#3F51B5"/>
          </svg>
        </div>
        <span class="nav-text">Dashboard</span>
      </div>

      <!-- 分隔线 -->
      <div class="nav-divider"></div>

      <!-- Settings -->
      <div class="nav-item" @click="$emit('navigate', 'settings')">
        <div class="nav-icon">
          <svg width="24" height="24" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="3" stroke="#00BCD4" stroke-width="1.5" fill="none"/>
            <circle cx="12" cy="12" r="1" stroke="#00BCD4" stroke-width="1.5" fill="none"/>
            <path d="M12 1L15.09 5.26L20 5.5L16.5 9L17.5 14L12 11.5L6.5 14L7.5 9L4 5.5L8.91 5.26L12 1Z" stroke="#00BCD4" stroke-width="1.5" fill="none"/>
          </svg>
        </div>
        <span class="nav-text">Settings</span>
        <div class="nav-arrow">
          <svg width="10" height="10" viewBox="0 0 10 10">
            <path d="M4 2L6 4L4 6" stroke="#646A77" stroke-width="1.5" fill="none" opacity="0.5"/>
          </svg>
        </div>
      </div>

      <!-- Help -->
      <div class="nav-item" @click="$emit('navigate', 'help')">
        <div class="nav-icon">
          <svg width="24" height="24" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" stroke="#00BCD4" stroke-width="1.5" fill="none"/>
            <path d="M8 10C8 8 10 6 12 6S16 8 16 10C16 11.5 14.5 12.5 12 13V14" stroke="#00BCD4" stroke-width="1.5" fill="none"/>
          </svg>
        </div>
        <span class="nav-text">Help</span>
      </div>

      <!-- 分隔线 -->
      <div class="nav-divider"></div>

      <!-- App -->
      <div class="nav-item" @click="$emit('navigate', 'app')">
        <div class="nav-icon">
          <svg width="24" height="24" viewBox="0 0 24 24">
            <rect x="6" y="5" width="12" height="14" rx="2" stroke="#00BCD4" stroke-width="1.5" fill="none"/>
            <circle cx="12" cy="15" r="1" fill="#00BCD4"/>
          </svg>
        </div>
        <span class="nav-text">App</span>
      </div>

      <!-- 用户头像 -->
      <div class="user-avatar">
        <img src="../assets/images/avatar.png" alt="User Avatar" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Sidebar',
  emits: ['navigate']
}
</script>

<style scoped>
.sidebar {
  width: 207px;
  height: 840px;
  background-color: #fff;
  border-right: 1px solid #edeef1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

/* Logo样式 */
.logo-section {
  padding: 20px 16px 16px;
  border-bottom: 1px solid #edeef1;
  margin-bottom: 16px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  position: relative;
  width: 38px;
  height: 38px;
}

.logo-shape {
  position: absolute;
  border-radius: 2px;
}

.shape1 {
  width: 11px;
  height: 11px;
  background-color: #f58147;
  top: 16px;
  left: 0;
}

.shape2 {
  width: 17px;
  height: 19px;
  background-color: #7babb1;
  top: 0;
  left: 6px;
}

.shape3 {
  width: 17px;
  height: 16px;
  background-color: #455d7e;
  top: 14px;
  right: 0;
}

.logo-text {
  font-family: 'Krona One', sans-serif;
  font-size: 24px;
  font-weight: 400;
  color: #000;
}

.sidebar-content {
  padding: 0 16px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 6px;
  position: relative;
}

.nav-item:hover {
  background-color: #f8f9fa;
}

.nav-item.active {
  background-color: #f0f8ff;
}

.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.nav-text {
  font-size: 14px;
  color: #21252e;
  font-weight: 400;
  flex: 1;
}

.nav-item.active .nav-text {
  font-weight: 700;
  color: #3f51b5;
}

.nav-arrow {
  position: absolute;
  right: 17px;
  top: 50%;
  transform: translateY(-50%);
}

.nav-divider {
  height: 1px;
  background-color: #edeef1;
  margin: 12px 0;
}

.user-avatar {
  margin-top: auto;
  padding-top: 20px;
  display: flex;
  justify-content: center;
}

.user-avatar img {
  width: 186px;
  height: 59px;
  object-fit: cover;
  border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid #edeef1;
  }
  
  .sidebar-content {
    flex-direction: row;
    padding: 10px;
    gap: 8px;
    overflow-x: auto;
  }
  
  .nav-item {
    flex-direction: column;
    padding: 8px;
    min-width: 60px;
    text-align: center;
  }
  
  .nav-text {
    font-size: 12px;
    margin-top: 4px;
  }
  
  .nav-arrow {
    display: none;
  }
  
  .nav-divider {
    width: 1px;
    height: 30px;
    margin: 0 8px;
  }
  
  .user-avatar {
    margin-top: 0;
    padding-top: 0;
  }
  
  .user-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
}
</style>
