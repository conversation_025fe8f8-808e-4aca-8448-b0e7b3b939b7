<template>
  <div class="stream-test">
    <div class="panel">
      <h2>WebSocket 流式转写原型</h2>
      <div class="row">
        <strong>语言:</strong>
        <select v-model="language">
          <option value="zh">中文</option>
          <option value="en">英文</option>
        </select>
      </div>
      <div class="row">
        <button @click="start" :disabled="isRunning">开始</button>
        <button @click="stop" :disabled="!isRunning">停止</button>
      </div>
      <div class="row">
        <strong>状态:</strong>&nbsp;{{ status }}
      </div>
      <div class="row">
        <div class="meter"><div class="bar" :style="{ width: meterPercent + '%' }"></div></div>
        <span class="hint">输入音量</span>
      </div>
      <div class="row">
        <strong>采样率:</strong>&nbsp;{{ sampleRate || '-' }} Hz
      </div>
      <div class="row">
        <strong>连接:</strong>&nbsp;{{ wsState }}
      </div>
      <div class="row">
        <strong>识别:</strong>
        <pre class="result">{{ resultText }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StreamTest',
  data() {
    return {
      status: '待机',
      isRunning: false,
      ws: null,
      wsState: 'disconnected',
      audioContext: null,
      workletNode: null,
      sourceNode: null,
      sampleRate: 0,
      language: 'zh',
      floatQueue: [], // Float32 分片队列
      accFloat: null, // 累计缓冲
      frameSize: 0, // 20ms 帧大小（sampleRate/50）
      meter: 0,
      resultText: ''
    }
  },
  computed: {
    meterPercent() { return Math.max(0, Math.min(100, this.meter * 200)).toFixed(0) }
  },
  methods: {
    async start() {
      try {
        this.status = '请求音频权限...'
        this.isRunning = true
        // 音频
        const stream = await navigator.mediaDevices.getUserMedia({ audio: { channelCount: 1, echoCancellation: true, noiseSuppression: true, autoGainControl: true } })
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)({ latencyHint: 'interactive' })
        await this.audioContext.audioWorklet.addModule('/worklets/pcm-processor.js')
        this.sampleRate = this.audioContext.sampleRate
        this.frameSize = Math.round(this.sampleRate / 50) // 20ms 帧
        this.sourceNode = this.audioContext.createMediaStreamSource(stream)
        this.workletNode = new AudioWorkletNode(this.audioContext, 'pcm-processor')
        this.workletNode.port.onmessage = (e) => {
          if (!e.data || e.data.type !== 'audio') return
          const float32 = e.data.samples
          // RMS 计算
          let sum = 0; for (let i = 0; i < float32.length; i++) sum += float32[i] * float32[i]
          this.meter = Math.sqrt(sum / float32.length)
          // 入队
          this.enqueueFloat(float32)
          this.flushFrames()
        }
        this.sourceNode.connect(this.workletNode)
        this.workletNode.connect(this.audioContext.destination)

        // WS
        await this.openWS()
        this.status = '录音中（流式发送）...'
      } catch (e) {
        console.error(e)
        this.status = '启动失败: ' + e.message
        this.isRunning = false
      }
    },

    async openWS() {
        const url = (location.protocol === 'https:' ? 'wss://' : 'ws://') + (location.hostname + ':8001') + '/ws/stream'
        this.ws = new WebSocket(url)
        this.ws.binaryType = 'arraybuffer'
        this.wsState = 'connecting'
        this.ws.onopen = () => {
          this.wsState = 'connected'
          this.ws.send(JSON.stringify({ event: 'start', sampleRate: this.sampleRate, language: this.language }))
        }
        this.ws.onmessage = (evt) => {
          try {
            const msg = JSON.parse(evt.data)
            if (msg.event === 'ack') {
              this.status = '开始流式发送...'
            } else if (msg.event === 'final') {
              this.resultText = msg.text || ''
              this.status = '已收到最终结果'
              try { this.ws.close() } catch {}
            } else if (msg.event === 'error') {
              this.status = '服务错误: ' + msg.message
            }
          } catch (_) {}
        }
        this.ws.onclose = () => { this.wsState = 'disconnected' }
        this.ws.onerror = () => { this.wsState = 'error' }
    },

    enqueueFloat(chunk) {
      if (!this.accFloat || this.accFloat.length === 0) {
        this.accFloat = chunk
      } else {
        const merged = new Float32Array(this.accFloat.length + chunk.length)
        merged.set(this.accFloat, 0)
        merged.set(chunk, this.accFloat.length)
        this.accFloat = merged
      }
    },

    flushFrames() {
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) return
      while (this.accFloat && this.accFloat.length >= this.frameSize) {
        const frame = this.accFloat.subarray(0, this.frameSize)
        const rest = this.accFloat.subarray(this.frameSize)
        // float32 -> int16 小端
        const ab = new ArrayBuffer(frame.length * 2)
        const view = new DataView(ab)
        for (let i = 0; i < frame.length; i++) {
          let s = Math.max(-1, Math.min(1, frame[i]))
          view.setInt16(i * 2, s < 0 ? s * 0x8000 : s * 0x7FFF, true)
        }
        this.ws.send(ab)
        // 余量保留
        const remain = new Float32Array(rest.length)
        remain.set(rest, 0)
        this.accFloat = remain
      }
    },

    stop() {
      // 停止音频采集，但保留 WS 连接等待服务端返回 final
      try {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
          this.ws.send(JSON.stringify({ event: 'end' }))
        }
      } catch {}
      // 关闭本地音频链路
      try { if (this.workletNode) this.workletNode.disconnect() } catch {}
      try { if (this.sourceNode) this.sourceNode.disconnect() } catch {}
      try { if (this.audioContext && this.audioContext.state !== 'closed') this.audioContext.close() } catch {}
      this.workletNode = null; this.sourceNode = null; this.audioContext = null
      this.isRunning = false
      this.status = '已停止，等待识别结果...'
    },

    cleanup() {
      this.isRunning = false
      try { if (this.workletNode) this.workletNode.disconnect() } catch {}
      try { if (this.sourceNode) this.sourceNode.disconnect() } catch {}
      try { if (this.audioContext && this.audioContext.state !== 'closed') this.audioContext.close() } catch {}
      try { if (this.ws && this.ws.readyState === WebSocket.OPEN) this.ws.close() } catch {}
      this.ws = null; this.accFloat = null
    }
  },
  beforeUnmount() { this.cleanup() }
}
</script>

<style scoped>
.stream-test { display: flex; justify-content: center; padding: 24px; }
.panel { max-width: 720px; width: 100%; background: #fff; border-radius: 12px; padding: 16px 20px; box-shadow: 0 6px 20px rgba(0,0,0,.08); }
.row { margin: 10px 0; display: flex; align-items: center; gap: 10px; }
.meter { width: 280px; height: 10px; background: #eee; border-radius: 6px; overflow: hidden; }
.bar { width: 0%; height: 100%; background: #4caf50; transition: width 80ms linear; }
.hint { color: #666; font-size: 12px; }
.result { white-space: pre-wrap; background: #f8f9fa; border-radius: 8px; padding: 8px; min-height: 48px; flex: 1; }
button { padding: 6px 12px; }
</style>
