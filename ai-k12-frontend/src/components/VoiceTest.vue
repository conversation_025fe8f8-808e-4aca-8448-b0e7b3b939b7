<template>
  <div class="voice-test">
    <div class="container">
      <h1 class="title">语音输入小助手</h1>
      
      <button 
        id="recordButton" 
        class="record-button"
        @pointerdown="startRecording"
        @pointerup="stopRecording"
        @mousedown="startRecording"
        @mouseup="stopRecording"
        @touchstart.prevent="startRecording"
        @touchend.prevent="stopRecording"
        :class="{ recording: isRecording }"
      >
        🎙️
      </button>
      
      <p class="status" :class="{ error: hasError, recording: isRecording, processing: isProcessing }">{{ statusText }}</p>
      <div class="result" :class="{ 'has-content': resultText }">{{ resultText }}</div>

      <!-- 录音指示器 -->
      <div v-if="isRecording" class="recording-indicator">
        <div class="recording-dot"></div>
        <span>正在录音... {{ recordingTimeDisplay }}</span>
        <div class="recording-hint">请清晰地说出你要识别的内容，建议3-5秒</div>
      </div>

      <!-- 输入音量电平 -->
      <div class="level-row">
        <div class="level-meter" title="输入音量 (RMS)">
          <div class="level-bar" :style="{ width: meterPercent + '%' }"></div>
        </div>
        <span class="level-hint">若说话时条不动，说明采集不到声音</span>
      </div>

      <!-- 调试信息面板 -->
      <div class="debug-panel" v-if="showDebug">
        <h3>🔧 调试信息</h3>
        <div class="debug-item">
          <strong>API端点:</strong> {{ apiEndpoint }}
        </div>
        <div class="debug-item">
          <strong>录音状态:</strong> {{ isRecording ? '录音中' : '未录音' }}
        </div>
        <div class="debug-item">
          <strong>MediaRecorder状态:</strong> {{ mediaRecorderState }}
        </div>
        <div class="debug-item">
          <strong>音频块数量:</strong> {{ audioChunks.length }}
        </div>
        <div class="debug-item">
          <strong>最后数据块大小(bytes):</strong> {{ lastChunkSize }}
        </div>
        <div class="debug-item">
          <strong>录音时长(ms):</strong>
          {{ recordingStartTime && recordingEndTime ? (recordingEndTime - recordingStartTime) : (isRecording && recordingStartTime ? (Date.now() - recordingStartTime) : '-') }}
        </div>
        <div class="debug-item">
          <strong>发送时延(ms):</strong>
          {{ sendTiming.duration != null ? sendTiming.duration : (sendTiming.start ? (Date.now() - sendTiming.start) : '-') }}
        </div>
        <div class="debug-item">
          <strong>浏览器支持:</strong> {{ browserSupport }}
        </div>
        <div class="debug-item">
          <strong>权限状态:</strong> {{ permissionState }}
        </div>
        <div class="debug-item">
          <strong>输入设备:</strong>
          <ul>
            <li v-for="dev in inputDevices" :key="dev.deviceId">{{ dev.label || '未命名设备' }} (id: {{ dev.deviceId }})</li>
            <li v-if="!inputDevices.length">(无可用输入设备或未授权)</li>
          </ul>
        </div>
        <div class="debug-item">
          <strong>支持的 MIME:</strong> {{ supportedMimeTypes.length ? supportedMimeTypes.join(', ') : '未知' }}
        </div>
        <div class="debug-item">
          <strong>使用的 MIME:</strong> {{ chosenMimeType || '-' }}
        </div>
        <div class="debug-item" v-if="lastError">
          <strong>最后错误:</strong> {{ lastError }}
        </div>
        <div class="debug-item" v-if="lastResponse">
          <strong>最后响应:</strong> {{ lastResponse }}
        </div>
      </div>

      <!-- 调试开关 -->
      <button class="debug-toggle" @click="toggleDebug">
        {{ showDebug ? '隐藏调试' : '显示调试' }}
      </button>

      <!-- 诊断工具 -->
      <div class="diagnostic-panel" v-if="showDiagnostic">
        <h3>🔍 API连接诊断</h3>
        <div class="method-selector">
          <label for="httpMethod">HTTP方法:</label>
          <select id="httpMethod" v-model="httpMethod" class="method-select">
            <option value="POST">POST</option>
            <option value="PUT">PUT</option>
            <option value="PATCH">PATCH</option>
          </select>
          <span class="method-info">当前使用: {{ httpMethod }}</span>
        </div>

        <div class="device-selector">
          <label for="audioDevice">麦克风设备:</label>
          <select id="audioDevice" v-model="selectedDeviceId" class="device-select">
            <option value="">自动选择</option>
            <option v-for="device in inputDevices" :key="device.deviceId" :value="device.deviceId">
              {{ device.label || '未命名设备' }}
            </option>
          </select>
          <button class="refresh-devices" @click="updateInputDevices" title="刷新设备列表">🔄</button>
        </div>

        <!-- 采集约束开关与监听 -->
        <div class="constraints-row">
          <label><input type="checkbox" v-model="echoCancellation"> 回声消除</label>
          <label><input type="checkbox" v-model="noiseSuppression"> 噪声抑制</label>
          <label><input type="checkbox" v-model="autoGainControl"> 自动增益</label>
          <button class="diagnostic-button" @click="toggleMonitor" :disabled="!isRecording">{{ isMonitoring ? '关闭监听' : '开启监听' }}</button>
        </div>

        <div class="param-selector">
          <div class="param-group">
            <label for="audioParam">音频参数名:</label>
            <select id="audioParam" v-model="audioParamName" class="param-select">
              <option value="audio">audio</option>
              <option value="audio_file">audio_file</option>
              <option value="file">file</option>
              <option value="audio_data">audio_data</option>
              <option value="recording">recording</option>
            </select>
          </div>
          <div class="param-group">
            <label for="languageParam">语言参数名:</label>
            <select id="languageParam" v-model="languageParamName" class="param-select">
              <option value="language">language</option>
              <option value="lang">lang</option>
              <option value="language_code">language_code</option>
              <option value="locale">locale</option>
            </select>
          </div>
        </div>
        <div class="file-upload-test">
          <input type="file" id="testFileInput" accept="audio/*" class="file-input" ref="fileInput">
          <button class="diagnostic-button" @click="testFileUpload">
            上传文件测试
          </button>
        </div>

        <button class="diagnostic-button" @click="testApiConnection">
          测试API连接
        </button>
        <button class="diagnostic-button" @click="testAudioFormat">
          测试音频格式
        </button>
        <div class="diagnostic-result" v-if="diagnosticResult">
          <strong>诊断结果:</strong>
          <pre>{{ diagnosticResult }}</pre>
        </div>
      </div>

      <button class="diagnostic-toggle" @click="toggleDiagnostic">
        {{ showDiagnostic ? '隐藏诊断' : '显示诊断' }}
      </button>
    </div>
  </div>
</template>

<script>

import axios from 'axios'
import { encodeWAV } from '../utils/wavEncoder'

export default {
  name: 'VoiceTest',
  data() {
    return {
      isRecording: false,
      isProcessing: false,
      hasError: false,
      statusText: '按住按钮，开始说话',
      resultText: '',
      mediaRecorder: null,
      audioChunks: [],
      // Web Audio 采集（生成真正的 PCM/WAV）
      audioContext: null,
      sourceNode: null,
      processorNode: null,
      pcmChunks: [], // Float32 分片
      inputSampleRate: 0,
      usePCM: true, // 使用 WebAudio -> WAV 路径，避免非 WAV 容器导致后端解析失败
      apiEndpoint: 'http://localhost:8001/transcribe',
      httpMethod: 'POST', // 可以是 'POST', 'PUT', 'PATCH' 等
      audioParamName: 'audio_file', // 音频文件参数名
      languageParamName: 'language', // 语言参数名
      selectedDeviceId: null, // 选中的麦克风设备ID
      // 调试相关数据
      showDebug: false,
      showDiagnostic: false,
      lastError: null,
      lastResponse: null,
      diagnosticResult: null,
      browserSupport: '',
      // 新增调试字段
      permissionState: 'unknown',
      inputDevices: [],
      supportedMimeTypes: [],
      chosenMimeType: null,
      recordingStartTime: null,
      recordingEndTime: null,
      lastChunkSize: 0,
  totalAudioSize: 0,
      // 录音约束与监听
      echoCancellation: true,
      noiseSuppression: true,
      autoGainControl: true,
      isMonitoring: false,
      monitorAudioEl: null,
      // 电平
      meterValue: 0,
      // 全局抬起事件处理器（用于保证用户按住移动到按钮外也能正常结束录音）
      globalUpHandler: null,
      sendTiming: {
        start: null,
        end: null,
        duration: null,
        status: null
      }
    }
  },
  computed: {
    mediaRecorderState() {
      return this.mediaRecorder ? this.mediaRecorder.state : '未初始化'
    },
    recordingTimeDisplay() {
      if (!this.recordingStartTime) return ''
      const elapsed = this.isRecording
        ? Date.now() - this.recordingStartTime
        : this.recordingEndTime - this.recordingStartTime
      const seconds = Math.floor(elapsed / 1000)
      const milliseconds = Math.floor((elapsed % 1000) / 100)
      return `${seconds}.${milliseconds}s`
    },
    meterPercent() {
      const pct = Math.max(0, Math.min(100, this.meterValue * 200))
      return pct.toFixed(0)
    }
  },
  methods: {
    async checkBrowserSupport() {
      const support = {
        mediaDevices: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
        mediaRecorder: typeof MediaRecorder !== 'undefined',
        permissions: !!(navigator.permissions && navigator.permissions.query),
        enumerateDevices: !!(navigator.mediaDevices && navigator.mediaDevices.enumerateDevices)
      }
      return Object.entries(support).map(([k, v]) => `${k}:${v}`).join(' | ')
    },

    async getSupportedMimeTypes() {
      const types = []
      // 常见音频 mime 列表，按优先级
      const candidates = [
        'audio/webm;codecs=opus',
        'audio/webm',
        'audio/ogg;codecs=opus',
        'audio/ogg',
        'audio/mp4',
        'audio/wav'
      ]
      for (const t of candidates) {
        try {
          if (MediaRecorder.isTypeSupported && MediaRecorder.isTypeSupported(t)) types.push(t)
        } catch (e) {
          // ignore
        }
      }
      this.supportedMimeTypes = types
      this.chosenMimeType = types.length ? types[0] : null
      return types
    },

    async updateInputDevices() {
      if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) return
      try {
        const devices = await navigator.mediaDevices.enumerateDevices()
        this.inputDevices = devices.filter(d => d.kind === 'audioinput')
      } catch (err) {
        // ignore
      }
    },

    async updatePermissionStatus() {
      if (!navigator.permissions || !navigator.permissions.query) {
        this.permissionState = 'unsupported'
        return
      }
      try {
        const status = await navigator.permissions.query({ name: 'microphone' })
        this.permissionState = status.state
        status.onchange = () => { this.permissionState = status.state }
      } catch (err) {
        // some browsers may throw; fall back
        this.permissionState = 'unknown'
      }
    },

    async startRecording() {
      if (this.isRecording) return

      try {
        // 采集约束，跟随诊断页设置
        const constraints = {
          audio: {
            channelCount: 1,
            echoCancellation: this.echoCancellation,
            noiseSuppression: this.noiseSuppression,
            autoGainControl: this.autoGainControl
          }
        }

        // 如果指定了设备ID，添加到约束中
        if (this.selectedDeviceId) {
          constraints.audio.deviceId = { exact: this.selectedDeviceId }
          console.debug('使用指定的麦克风设备:', this.selectedDeviceId)
        }
        const stream = await navigator.mediaDevices.getUserMedia(constraints)

        // 使用 Web Audio 捕获原始 PCM，后端最容易解析（WAV/PCM16）
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
          latencyHint: 'interactive'
        })
        this.inputSampleRate = this.audioContext.sampleRate
        this.sourceNode = this.audioContext.createMediaStreamSource(stream)
        // ScriptProcessor 已废弃但兼容性最好；如果需要可升级到 AudioWorklet
        const bufferSize = 4096
        this.processorNode = this.audioContext.createScriptProcessor(bufferSize, 1, 1)
        this.pcmChunks = []
        this.processorNode.onaudioprocess = (e) => {
          const channelData = e.inputBuffer.getChannelData(0)
          // 拷贝一份，避免复用底层缓冲区
          this.pcmChunks.push(new Float32Array(channelData))
          // 更新电平（RMS）
          let sum = 0
          for (let i = 0; i < channelData.length; i++) sum += channelData[i] * channelData[i]
          const rms = Math.sqrt(sum / channelData.length)
          this.meterValue = rms
        }
        this.sourceNode.connect(this.processorNode)
        this.processorNode.connect(this.audioContext.destination)

        this.recordingStartTime = Date.now()
        this.recordingEndTime = null
        this.audioChunks = []
        this.isRecording = true
        this.hasError = false
        this.statusText = "正在听你说话... 松开结束"
        this.resultText = ""

        // 监听全局的抬起事件，避免移动到按钮外导致无法触发 stop
        if (!this.globalUpHandler) {
          const handler = () => {
            // 清理监听器
            window.removeEventListener('pointerup', handler)
            window.removeEventListener('mouseup', handler)
            window.removeEventListener('touchend', handler)
            this.globalUpHandler = null
            // 结束录音
            this.stopRecording()
          }
          this.globalUpHandler = handler
          window.addEventListener('pointerup', handler, { once: true })
          window.addEventListener('mouseup', handler, { once: true })
          window.addEventListener('touchend', handler, { once: true })
        }
        // 可选监听（直通本地播放，便于诊断）
        if (this.isMonitoring) {
          this.attachMonitor(this.sourceNode.mediaStream)
        }
      } catch (err) {
        console.error("无法获取麦克风:", err)
        this.hasError = true
        this.statusText = "无法使用麦克风哦！"
      }
    },

    stopRecording() {
      if (!this.isRecording) return
      this.recordingEndTime = Date.now()

      // 停止 WebAudio 采集
      try {
        this.isRecording = false
        this.isProcessing = true
        this.statusText = "正在努力识别..."

        if (this.processorNode) {
          this.processorNode.disconnect()
        }
        if (this.sourceNode) {
          this.sourceNode.disconnect()
        }
        if (this.audioContext) {
          this.audioContext.suspend().catch(() => {})
        }
        // 停止底层媒体流
        if (this.sourceNode && this.sourceNode.mediaStream) {
          this.sourceNode.mediaStream.getTracks().forEach(t => t.stop())
        }
      } catch (e) {
        console.warn('停止录音时出错:', e)
      }

      // 构建 WAV 并发送
      this.prepareWavAndSend()
    },

    async prepareWavAndSend() {
      try {
        // 拼接 Float32 PCM
        const totalLength = this.pcmChunks.reduce((a, c) => a + c.length, 0)
        const float32Data = new Float32Array(totalLength)
        let offset = 0
        for (const chunk of this.pcmChunks) {
          float32Data.set(chunk, offset)
          offset += chunk.length
        }

        // 使用 OfflineAudioContext 高质量重采样到 16k，避免 ScriptProcessor 简陋平均产生的抖动
        const fromRate = this.inputSampleRate || 48000
        const toRate = 16000
        const resampled = await this.resamplePCM(float32Data, fromRate, toRate)
        const wavBytes = encodeWAV(resampled, toRate, toRate)
        const wavBlob = new Blob([wavBytes], { type: 'audio/wav' })

        // 调试统计
        this.audioChunks = [wavBlob]
        this.lastChunkSize = wavBlob.size
        this.totalAudioSize = wavBlob.size
        this.chosenMimeType = 'audio/wav'

        console.debug(`生成 WAV 成功: ${wavBlob.size} bytes, 采样率 ${toRate}`)

        // 发送
        this.sendAudio()
      } catch (e) {
        console.error('生成 WAV 失败:', e)
        this.hasError = true
        this.isProcessing = false
        this.statusText = '生成音频失败，请重试'
      }
    },

    async resamplePCM(float32Data, fromRate, toRate) {
      if (fromRate === toRate) return float32Data
      const OfflineCtx = window.OfflineAudioContext || window.webkitOfflineAudioContext
      if (!OfflineCtx) {
        // 回退（不会更好，但保证不崩）
        return float32Data
      }
      // 创建输入缓冲
      const lengthIn = float32Data.length
      const audioCtxTmp = new (window.AudioContext || window.webkitAudioContext)()
      const inputBuffer = audioCtxTmp.createBuffer(1, lengthIn, fromRate)
      inputBuffer.copyToChannel(float32Data, 0, 0)
      await audioCtxTmp.close()

      // 离线渲染到目标采样率
      const targetLength = Math.ceil(lengthIn * toRate / fromRate)
      const offline = new OfflineCtx(1, targetLength, toRate)
      const src = offline.createBufferSource()
      src.buffer = inputBuffer
      src.connect(offline.destination)
      src.start(0)
      const rendered = await offline.startRendering()
      const out = new Float32Array(rendered.length)
      rendered.copyFromChannel(out, 0, 0)
      return out
    },

    attachMonitor(stream) {
      try {
        if (!this.monitorAudioEl) {
          this.monitorAudioEl = new Audio()
          this.monitorAudioEl.controls = true
          this.monitorAudioEl.autoplay = true
          this.monitorAudioEl.style.display = 'none'
          document.body.appendChild(this.monitorAudioEl)
        }
        this.monitorAudioEl.srcObject = stream
      } catch (e) {
        // ignore
      }
    },

    toggleMonitor() {
      this.isMonitoring = !this.isMonitoring
      if (this.isMonitoring && this.sourceNode && this.sourceNode.mediaStream) {
        this.attachMonitor(this.sourceNode.mediaStream)
      } else if (this.monitorAudioEl) {
        try { this.monitorAudioEl.srcObject = null } catch (e) {}
      }
    },

    async sendAudio() {
      this.isProcessing = true

      // 计算 chunk 列表与总大小，便于调试
      try {
        const sizes = this.audioChunks.map(c => (c && c.size) ? c.size : 0)
        this.lastChunkSize = sizes.length ? sizes[sizes.length - 1] : 0
        this.totalAudioSize = sizes.reduce((a, b) => a + b, 0)
        console.debug('音频数据统计:', {
          chunkCount: this.audioChunks.length,
          chunkSizes: sizes,
          totalSize: this.totalAudioSize,
          recordingDuration: this.recordingEndTime - this.recordingStartTime
        })

        // 检查是否有有效的音频数据
        if (this.totalAudioSize === 0 || this.audioChunks.length === 0) {
          throw new Error('没有录制到有效的音频数据')
        }

        // 检查录音时长是否太短
        const duration = this.recordingEndTime - this.recordingStartTime
        if (duration < 1000) { // 小于1秒的录音可能无效
          throw new Error('录音时长太短，请说长一些再松开（建议3-5秒）')
        } else if (duration < 2000) {
          console.warn(`录音时长较短: ${duration}ms，建议说长一些以获得更好识别效果`)
        }
      } catch (e) {
        console.error('音频数据检查失败:', e)
        this.hasError = true
        this.statusText = e.message || "录音数据有问题，请重试"
        this.lastError = e.message || String(e)
        return
      }

      const mime = this.chosenMimeType || 'audio/wav'
      const ext = mime.includes('wav') ? 'wav' : (mime.includes('ogg') ? 'ogg' : (mime.includes('mp4') ? 'mp4' : 'webm'))
      const audioBlob = this.audioChunks && this.audioChunks.length
        ? this.audioChunks[0]
        : new Blob([], { type: mime })

      console.debug(`创建音频Blob: ${audioBlob.size} bytes, 类型: ${mime}`)

      const formData = new FormData()
      formData.append(this.audioParamName, audioBlob, `recording.${ext}`)
      formData.append(this.languageParamName, 'zh-CN')

      this.sendTiming.start = Date.now()
      try {
        console.debug('开始发送音频数据到服务器...')

              // 根据配置使用不同的HTTP方法
      console.debug(`使用 ${this.httpMethod} 方法发送请求到: ${this.apiEndpoint}`)
      console.debug(`参数配置 - 音频: ${this.audioParamName}, 语言: ${this.languageParamName}`)

      const config = {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        timeout: 300000 // 5分钟超时，与后端脚本一致
      }

        let response
        switch (this.httpMethod.toUpperCase()) {
          case 'POST':
            response = await axios.post(this.apiEndpoint, formData, config)
            break
          case 'PUT':
            response = await axios.put(this.apiEndpoint, formData, config)
            break
          case 'PATCH':
            response = await axios.patch(this.apiEndpoint, formData, config)
            break
          default:
            throw new Error(`不支持的HTTP方法: ${this.httpMethod}`)
        }

        this.sendTiming.end = Date.now()
        this.sendTiming.duration = this.sendTiming.end - this.sendTiming.start
        this.sendTiming.status = response.status

        console.debug('服务器响应:', response.status, response.data)
        console.debug('完整响应对象:', response)

        // axios 的响应数据在 data 属性中
        const data = response.data
        this.lastResponse = data

        // 改进响应处理，支持多种响应格式
        let transcriptionText = null

        if (data && typeof data === 'object' && data.text) {
          // JSON格式: { text: "..." }
          transcriptionText = data.text
          console.debug('JSON格式响应，提取text字段:', transcriptionText)
        } else if (data && typeof data === 'string' && data.trim()) {
          // 纯文本格式
          transcriptionText = data.trim()
          console.debug('纯文本格式响应:', transcriptionText)
        } else if (typeof response.data === 'string' && response.data.trim()) {
          // 直接的字符串响应
          transcriptionText = response.data.trim()
          console.debug('直接字符串响应:', transcriptionText)
        }

        if (transcriptionText) {
          this.resultText = transcriptionText
          this.statusText = "识别完成！按住按钮继续说话"
          this.hasError = false
          this.isProcessing = false
          console.debug('语音识别成功:', transcriptionText)
        } else {
          console.error('响应数据:', data)
          console.error('响应类型:', typeof data)
          throw new Error(`服务器返回了意外的响应格式或空内容: ${JSON.stringify(data)}`)
        }

        // 清理 mediaRecorder 引用，准备下次录音
        try { this.mediaRecorder = null } catch (e) {}
      } catch (error) {
        console.error('识别失败:', error)
        this.hasError = true
        this.statusText = "哎呀，没听清，再试一次吧！"
        this.lastError = (error && error.message) ? error.message : String(error)
        this.sendTiming.end = this.sendTiming.end || Date.now()
        this.sendTiming.duration = this.sendTiming.end - (this.sendTiming.start || this.sendTiming.end)
        this.isProcessing = false
        try { this.mediaRecorder = null } catch (e) {}
      }
    },

    toggleDebug() {
      this.showDebug = !this.showDebug
    },

    toggleDiagnostic() {
      this.showDiagnostic = !this.showDiagnostic
    },

    async testApiConnection() {
      this.diagnosticResult = '正在测试API连接...'
      try {
        console.log('测试API连接:', this.apiEndpoint)

        // 先尝试简单的GET请求（如果支持的话）
        let response
        try {
          response = await axios.get(this.apiEndpoint.replace('/transcribe', '/health'), { timeout: 5000 })
        } catch (e) {
          // 如果GET失败，尝试OPTIONS请求
          console.log('GET请求失败，尝试OPTIONS:', e.message)
          response = await axios.options(this.apiEndpoint, { timeout: 5000 })
        }

        this.diagnosticResult = `✅ API连接成功\n状态码: ${response.status}\n响应: ${JSON.stringify(response.data, null, 2)}`

      } catch (error) {
        console.error('API连接测试失败:', error)
        this.diagnosticResult = `❌ API连接失败\n错误类型: ${error.name}\n错误信息: ${error.message}\n状态码: ${error.response?.status || '无'}\n响应: ${JSON.stringify(error.response?.data || '无', null, 2)}`
      }
    },

    async testFileUpload() {
      const fileInput = this.$refs.fileInput
      if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
        this.diagnosticResult = '❌ 请先选择一个音频文件'
        return
      }

      const file = fileInput.files[0]
      this.diagnosticResult = `正在上传文件: ${file.name} (${(file.size / 1024).toFixed(1)} KB)`

      try {
        const formData = new FormData()
        formData.append(this.audioParamName, file)
        formData.append(this.languageParamName, 'zh-CN')

        console.debug(`上传文件测试 - 参数: ${this.audioParamName}, ${this.languageParamName}`)

        const config = {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          timeout: 300000
        }

        let response
        switch (this.httpMethod.toUpperCase()) {
          case 'POST':
            response = await axios.post(this.apiEndpoint, formData, config)
            break
          case 'PUT':
            response = await axios.put(this.apiEndpoint, formData, config)
            break
          case 'PATCH':
            response = await axios.patch(this.apiEndpoint, formData, config)
            break
        }

        const data = response.data
        let transcriptionText = null

        if (data && typeof data === 'object' && data.text) {
          transcriptionText = data.text
        } else if (data && typeof data === 'string' && data.trim()) {
          transcriptionText = data.trim()
        } else if (typeof response.data === 'string' && response.data.trim()) {
          transcriptionText = response.data.trim()
        }

        this.diagnosticResult = `✅ 文件上传成功！\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}\n转录结果: ${transcriptionText || '无'}`

      } catch (error) {
        console.error('文件上传测试失败:', error)
        this.diagnosticResult = `❌ 文件上传失败\n错误类型: ${error.name}\n错误信息: ${error.message}\n状态码: ${error.response?.status || '无'}\n响应: ${JSON.stringify(error.response?.data || '无', null, 2)}`
      }
    },

    async testAudioFormat() {
      this.diagnosticResult = '正在测试音频格式...'
      try {
        // 创建一个测试音频文件
        const testAudio = new Uint8Array(1024) // 1KB测试数据
        for (let i = 0; i < testAudio.length; i++) {
          testAudio[i] = Math.random() * 255
        }

        const audioBlob = new Blob([testAudio], { type: 'audio/wav' })
        const formData = new FormData()
        formData.append(this.audioParamName, audioBlob, 'test.wav')
        formData.append(this.languageParamName, 'zh-CN')

        console.log('测试音频数据大小:', audioBlob.size)

        const response = await axios.post(this.apiEndpoint, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          timeout: 300000
        })

        const data = response.data
        let transcriptionText = null

        if (data && typeof data === 'object' && data.text) {
          transcriptionText = data.text
        } else if (data && typeof data === 'string' && data.trim()) {
          transcriptionText = data.trim()
        } else if (typeof response.data === 'string' && response.data.trim()) {
          transcriptionText = response.data.trim()
        }

        this.diagnosticResult = `✅ 音频格式测试成功\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}\n转录结果: ${transcriptionText || '无'}`

      } catch (error) {
        console.error('音频格式测试失败:', error)
        this.diagnosticResult = `❌ 音频格式测试失败\n错误类型: ${error.name}\n错误信息: ${error.message}\n状态码: ${error.response?.status || '无'}\n响应: ${JSON.stringify(error.response?.data || '无', null, 2)}`
      }
    }
  },

  created() {
    // 初始化调试信息
    this.checkBrowserSupport().then(s => { this.browserSupport = s }).catch(() => {})
    this.updateInputDevices()
    this.updatePermissionStatus()
    this.getSupportedMimeTypes()
  },

  beforeUnmount() {
    // 组件销毁时停止录音/释放资源
    try {
      if (this.isRecording) {
        this.stopRecording()
      }
      if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
        this.mediaRecorder.stop()
        this.mediaRecorder.stream.getTracks().forEach(track => track.stop())
      }
      if (this.processorNode) this.processorNode.disconnect()
      if (this.sourceNode) this.sourceNode.disconnect()
      if (this.audioContext) this.audioContext.close()
      if (this.monitorAudioEl) {
        try { this.monitorAudioEl.srcObject = null } catch (e) {}
      }
    } catch (e) {
      // ignore
    }
  }
}
</script>

<style scoped>
.voice-test {
  font-family: sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: white;
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 100%;
}

.title {
  color: #333;
  margin-bottom: 30px;
  font-size: 28px;
  font-weight: bold;
  text-align: center;
}

.record-button {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: #4CAF50;
  border: none;
  color: white;
  font-size: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
  outline: none;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.record-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.record-button:active,
.record-button.recording {
  background-color: #f44336;
  transform: scale(0.95);
  box-shadow: 0 2px 10px rgba(244, 67, 54, 0.4);
}

.status {
  margin-top: 20px;
  font-size: 18px;
  color: #555;
  text-align: center;
  min-height: 25px;
  transition: color 0.3s ease;
}

.status.error {
  color: #f44336;
}

.status.recording {
  color: #ff9800;
  font-weight: bold;
}

.status.processing {
  color: #2196f3;
  font-weight: bold;
}

.result {
  margin-top: 20px;
  font-size: 24px;
  font-weight: bold;
  min-height: 30px;
  color: #333;
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #667eea;
  width: 100%;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.result.has-content {
  background: #e8f5e8;
  border-left-color: #4caf50;
  color: #2e7d32;
}

.recording-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 15px 0;
  font-weight: bold;
  color: #ff9800;
  gap: 8px;
}

/* 音量电平条 */
.level-row { display: flex; align-items: center; gap: 10px; margin: 8px 0 16px; }
.level-meter { width: 280px; height: 10px; background: #eee; border-radius: 6px; overflow: hidden; }
.level-bar { height: 100%; background: #4caf50; width: 0%; transition: width 80ms linear; }
.level-hint { color: #666; font-size: 12px; }

.recording-hint {
  font-size: 14px;
  font-weight: normal;
  color: #666;
  background: #fff3cd;
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid #ffeaa7;
}

.recording-dot {
  width: 12px;
  height: 12px;
  background: #ff9800;
  border-radius: 50%;
  margin-right: 10px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 诊断面板样式 */
.diagnostic-panel {
  margin-top: 20px;
  padding: 20px;
  background: #f0f8ff;
  border-radius: 10px;
  border: 2px solid #2196f3;
}

.diagnostic-panel h3 {
  margin: 0 0 15px 0;
  color: #1976d2;
  font-size: 18px;
  font-weight: bold;
}

.diagnostic-button {
  background: #2196f3;
  color: white;
  border: none;
  padding: 8px 16px;
  margin: 0 10px 10px 0;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.diagnostic-button:hover {
  background: #1976d2;
}

.diagnostic-result {
  margin-top: 15px;
  padding: 15px;
  background: white;
  border-radius: 5px;
  border-left: 4px solid #4caf50;
  font-family: monospace;
  font-size: 12px;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

.diagnostic-toggle {
  margin-top: 15px;
  background: #ff9800;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.diagnostic-toggle:hover {
  background: #f57c00;
}

.method-selector {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.method-selector label {
  font-weight: bold;
  color: #1976d2;
}

.method-select {
  padding: 5px 10px;
  border: 2px solid #2196f3;
  border-radius: 5px;
  background: white;
  font-size: 14px;
  cursor: pointer;
}

.method-info {
  font-size: 12px;
  color: #666;
  font-weight: normal;
}

.device-selector {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.device-selector label {
  font-weight: bold;
  color: #1976d2;
  white-space: nowrap;
}

.device-select {
  padding: 5px 10px;
  border: 2px solid #2196f3;
  border-radius: 5px;
  background: white;
  font-size: 12px;
  cursor: pointer;
  flex: 1;
  min-width: 200px;
}

.refresh-devices {
  padding: 5px 10px;
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 12px;
}

.refresh-devices:hover {
  background: #1976d2;
}

.constraints-row {
  display: flex;
  gap: 12px;
  align-items: center;
  margin: 8px 0;
}

.param-selector {
  margin-bottom: 15px;
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.param-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.param-group label {
  font-weight: bold;
  color: #1976d2;
  font-size: 12px;
}

.param-select {
  padding: 5px 10px;
  border: 2px solid #2196f3;
  border-radius: 5px;
  background: white;
  font-size: 12px;
  cursor: pointer;
  min-width: 120px;
}

.file-upload-test {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.file-input {
  padding: 5px;
  border: 2px solid #2196f3;
  border-radius: 5px;
  background: white;
  font-size: 12px;
  cursor: pointer;
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .container {
    padding: 30px 20px;
    margin: 10px;
  }
  
  .title {
    font-size: 24px;
    margin-bottom: 25px;
  }
  
  .record-button {
    width: 80px;
    height: 80px;
    font-size: 40px;
  }
  
  .status {
    font-size: 16px;
  }
  
  .result {
    font-size: 20px;
  }
}
</style>
