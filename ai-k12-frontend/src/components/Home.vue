<template>
  <div class="home">
    <!-- 主布局 -->
    <div class="main-layout">
      <!-- 左侧导航栏 -->
      <Sidebar @navigate="handleNavigate" />
      
      <!-- 主内容区域 -->
      <div class="main-content">
        <!-- 头部区域 -->
        <div class="header">
          <!-- 左侧空白区域，保持布局平衡 -->
          <div class="header-left"></div>
          
          <!-- 登录按钮 -->
          <div class="header-right">
            <button class="signin-btn">Sign in</button>
          </div>
        </div>

        <!-- 主要内容 -->
        <div class="content-area">
          <!-- 标题区域 -->
          <div class="title-section" :class="{ 'fade-out': hasMessages }">
            <h1 class="main-title">Think First, <span class="highlight">Answer Later</span><br><span class="subtitle-line">We think with you</span></h1>
          </div>

          <!-- 副标题 -->
          <div class="subtitle-section" :class="{ 'fade-out': hasMessages }">
            <p class="subtitle-text">We teach, not just answer</p>
          </div>

          <!-- 科目选择区域 -->
          <div class="subject-section">
            <div class="subject-grade-row">
              <GradeSelector 
                @grade-select="handleGradeSelect"
                :selected-grade="selectedGrade"
              />
              
              <SubjectSelector 
                @subject-select="handleSubjectSelect"
                :selected-subject="selectedSubject"
              />
            </div>
          </div>

          <!-- 年级选择和聊天区域 -->
          <div class="interaction-section">
            <div class="grade-and-chat">
              <!-- 聊天窗口 -->
              <ChatWindow
                ref="chatWindow"
                :subject="selectedSubject"
                :grade="selectedGrade"
                @send-message="handleSendMessage"
                @messages-changed="handleMessagesChanged"
              />
              
            </div>
          </div>
        </div>

        <!-- 页脚 -->
        <div class="footer" :class="{ 'fade-out': hasMessages }">
          <div class="footer-content">
            <div class="footer-left">
              <h3>Beyond the answer</h3>
              <p><EMAIL></p>
              <div class="social-links">
                <a href="#" class="social-link">📱</a>
                <a href="#" class="social-link">📘</a>
                <a href="#" class="social-link">📷</a>
                <a href="#" class="social-link">🎬</a>
              </div>
              <!-- 清空对话按钮 -->
              <button v-if="hasMessages" @click="clearMessages" class="clear-chat-btn">
                清空对话
              </button>
            </div>
            <div class="footer-middle">
              <h4>Company</h4>
              <ul>
                <li><a href="#">About Us</a></li>
              </ul>
            </div>
            <div class="footer-right">
              <h4>Legal</h4>
              <ul>
                <li><a href="#">Honor Code</a></li>
                <li><a href="#">Privacy Policy</a></li>
                <li><a href="#">Terms of Service</a></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Sidebar from './Sidebar.vue'
import SubjectSelector from './SubjectSelector.vue'
import GradeSelector from './GradeSelector.vue'
import ChatWindow from './ChatWindow.vue'

export default {
  name: 'Home',
  components: {
    Sidebar,
    SubjectSelector,
    GradeSelector,
    ChatWindow
  },
  data() {
    return {
      selectedSubject: '',
      selectedGrade: '',
      hasMessages: false
    }
  },
  methods: {
    handleNavigate(route) {
      console.log('导航到:', route)
      if (route === 'test') {
        this.$router.push('/test')
      }
    },
    
    handleSubjectSelect(subject) {
      this.selectedSubject = subject
      console.log('选择科目:', subject)
    },
    
    handleGradeSelect(grade) {
      this.selectedGrade = grade
      console.log('选择年级:', grade)
    },
    
    handleSendMessage(message) {
      console.log('发送消息:', message)
      // 消息由 ChatWindow 组件管理，这里不需要存储
    },
    
    handleMessagesChanged(hasMessages) {
      this.hasMessages = hasMessages
      console.log('Home组件收到消息状态变化:', hasMessages)
      console.log('当前hasMessages状态:', this.hasMessages)
    },
    
    clearMessages() {
      this.hasMessages = false
      // 通过 ref 调用 ChatWindow 的方法来清空消息
      if (this.$refs.chatWindow) {
        this.$refs.chatWindow.clearAllMessages()
      }
    }
  }
}
</script>

<style scoped>
.home {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(0deg, rgba(245, 246, 255, 0) 0%, rgba(246, 255, 254, 1) 0%);
}

.main-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  background-color: transparent;
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 11px 30px 0 18px;
  height: 60px;
  border-bottom: 1px solid #e4e6eb;
  background-color: #fff;
}

.header-left {
  flex: 1; /* 左侧空白区域 */
}

.header-right {
  display: flex;
  align-items: center;
}

.signin-btn {
  padding: 0 12px;
  height: 32px;
  background: rgba(110, 192, 250, 0.51);
  border: none;
  border-radius: 8px;
  color: #605f5f;
  font-weight: bold;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.signin-btn:hover {
  background: rgba(110, 192, 250, 0.7);
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 0 277px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  max-width: 1440px;
  margin: 0 auto;
  position: relative;
}

.title-section {
  text-align: center;
  margin-bottom: 30px;
  transition: all 0.3s ease;
  opacity: 1;
  transform: translateY(0);
}

.title-section.fade-out {
  opacity: 0;
  transform: translateY(-20px);
  pointer-events: none;
  height: 0;
  margin: 0;
  overflow: hidden;
  display: none;
}

.main-title {
  font-size: 22px;
  font-weight: 600;
  color: #21252e;
  line-height: 1.2;
  margin: 0 0 8px 0;
}

.main-title .highlight {
  color: #f58147;
}

.main-title .subtitle-line {
  font-size: 36px;
  font-weight: 700;
  color: #21252e;
  display: block;
  margin-top: 8px;
}

.subtitle-section {
  text-align: center;
  margin-bottom: 25px;
  margin-top: -30px;
  transition: all 0.3s ease;
  opacity: 1;
  transform: translateY(0);
}

.subtitle-section.fade-out {
  opacity: 0;
  transform: translateY(-20px);
  pointer-events: none;
  height: 0;
  margin: 0;
  overflow: hidden;
  display: none;
}

.subtitle-text {
  font-size: 15px;
  color: #888888;
  margin: 0;
  font-weight: 400;
  line-height: 1.2;
}

.subject-section {
  margin-bottom: 30px;
}

.subject-grade-row {
  display: flex;
  gap: 40px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.interaction-section {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}

.grade-and-chat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  width: 100%;
  max-width: 900px;
}

.clear-chat-section {
  text-align: center;
  margin-top: 20px;
}

.clear-chat-btn-main {
  padding: 12px 24px;
  background: #f58147;
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(245, 129, 71, 0.3);
}

.clear-chat-btn-main:hover {
  background: #e06a2e;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(245, 129, 71, 0.4);
}

.bottom-text {
  text-align: center;
  margin: 40px 0;
}

.bottom-text p {
  font-size: 20px;
  color: #7f7f7f;
  margin: 0;
}

/* 页脚样式 */
.footer {
  background-color: #fff;
  border-top: 1px solid #f5f5f7;
  padding: 60px 0;
  margin-top: auto;
  transition: all 0.3s ease;
  opacity: 1;
  transform: translateY(0);
}

.footer.fade-out {
  opacity: 0;
  transform: translateY(20px);
  pointer-events: none;
  height: 0;
  padding: 0;
  overflow: hidden;
  display: none;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  gap: 150px;
  padding: 0 50px;
}

.footer-left h3 {
  font-size: 20px;
  font-weight: bold;
  color: #21252e;
  margin-bottom: 8px;
}

.footer-left p {
  color: #21252e;
  margin-bottom: 10px;
}

.social-links {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.social-link {
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-size: 16px;
}

.clear-chat-btn {
  margin-top: 15px;
  padding: 8px 16px;
  background: #f58147;
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-chat-btn:hover {
  background: #e06a2e;
  transform: translateY(-1px);
}

.footer-middle h4,
.footer-right h4 {
  font-size: 16px;
  color: #21252e;
  margin-bottom: 20px;
}

.footer-middle ul,
.footer-right ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-middle li,
.footer-right li {
  margin-bottom: 8px;
}

.footer-middle a,
.footer-right a {
  color: #21252e;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s ease;
}

.footer-middle a:hover,
.footer-right a:hover {
  color: #007bff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-area {
    padding: 0 20px;
  }
  
  .footer-content {
    flex-direction: column;
    gap: 40px;
    padding: 0 20px;
  }
  
  .clear-chat-btn-main {
    padding: 10px 20px;
    font-size: 13px;
  }
}
</style>
