<template>
  <div class="subject-selector">
    <div class="subjects-grid">
      <!-- Math -->
      <div 
        class="subject-item" 
        :class="{ active: selectedSubject === 'Math' }"
        @click="selectSubject('Math')"
      >
        <div class="subject-icon">
          <svg width="26" height="26" viewBox="0 0 26 26">
            <rect x="2" y="2" width="22" height="22" rx="2" stroke="#3F51B5" stroke-width="2" fill="none"/>
            <circle cx="13" cy="13" r="7" fill="#8A8D9D" stroke="#3F51B5" stroke-width="1"/>
          </svg>
        </div>
        <span class="subject-name">Math</span>
      </div>

      <!-- Physics -->
      <div 
        class="subject-item" 
        :class="{ active: selectedSubject === 'Physics' }"
        @click="selectSubject('Physics')"
      >
        <div class="subject-icon">
          <svg width="26" height="26" viewBox="0 0 26 26">
            <circle cx="13" cy="13" r="9" stroke="#8A8D9D" stroke-width="1.86" fill="none"/>
            <circle cx="19" cy="8" r="4" stroke="#8A8D9D" stroke-width="1.86" fill="none"/>
          </svg>
        </div>
        <span class="subject-name">Physics</span>
      </div>

      <!-- Chemistry -->
      <div 
        class="subject-item" 
        :class="{ active: selectedSubject === 'Chemistry' }"
        @click="selectSubject('Chemistry')"
      >
        <div class="subject-icon">
          <svg width="26" height="26" viewBox="0 0 26 26">
            <g mask="url(#chemistry-mask)">
              <rect width="26" height="26" fill="#D9D9D9"/>
              <g transform="translate(6, 4)">
                <path d="M3 0L8 0M0 1L15 19M0 12L14 2" stroke="#8A8D9D" stroke-width="2"/>
                <circle cx="8" cy="8" r="1" fill="#8A8D9D"/>
              </g>
            </g>
          </svg>
        </div>
        <span class="subject-name">Chemistry</span>
      </div>

      <!-- Biology -->
      <div 
        class="subject-item" 
        :class="{ active: selectedSubject === 'Biology' }"
        @click="selectSubject('Biology')"
      >
        <div class="subject-icon">
          <svg width="26" height="26" viewBox="0 0 26 26">
            <g mask="url(#biology-mask)">
              <rect width="25" height="25" fill="#D9D9D9"/>
              <g transform="translate(4, 3)">
                <path d="M4 4L16 16M0 0L15 15" stroke="#8A8D9D" stroke-width="2"/>
              </g>
            </g>
            <path d="M5 9L15 19" stroke="#8A8D9D" stroke-width="2"/>
          </svg>
        </div>
        <span class="subject-name">Biology</span>
      </div>

      <!-- Chinese -->
      <div 
        class="subject-item" 
        :class="{ active: selectedSubject === 'Chinese' }"
        @click="selectSubject('Chinese')"
      >
        <div class="subject-icon">
          <svg width="25" height="25" viewBox="0 0 25 25">
            <g mask="url(#chinese-mask)">
              <rect width="25" height="25" fill="#D9D9D9"/>
              <g transform="translate(4, 3)">
                <path d="M0 0L17 14" stroke="#8A8D9D" stroke-width="2"/>
              </g>
            </g>
            <path d="M6 9L21 22" stroke="#8A8D9D" stroke-width="2"/>
          </svg>
        </div>
        <span class="subject-name">Chinese</span>
      </div>

      <!-- Science -->
      <div 
        class="subject-item" 
        :class="{ active: selectedSubject === 'Science' }"
        @click="selectSubject('Science')"
      >
        <div class="subject-icon">
          <svg width="25" height="25" viewBox="0 0 25 25">
            <path d="M3 2L19 20" stroke="#8A8D9D" stroke-width="2"/>
            <circle cx="7" cy="6" r="1" fill="#8A8D9D"/>
          </svg>
        </div>
        <span class="subject-name">Science</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SubjectSelector',
  props: {
    selectedSubject: {
      type: String,
      default: ''
    }
  },
  emits: ['subject-select'],
  methods: {
    selectSubject(subject) {
      this.$emit('subject-select', subject)
    }
  }
}
</script>

<style scoped>
.subject-selector {
  display: flex;
  justify-content: center;
  padding: 0;
}

.subjects-grid {
  display: flex;
  gap: 28px;
  flex-wrap: wrap;
  justify-content: center;
  max-width: 609px;
  align-items: center;
}

.subject-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 9px;
  padding: 8px;
  min-width: 54px;
}

.subject-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.subject-item.active {
  background-color: #f0f8ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(63, 81, 181, 0.2);
}

.subject-icon {
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.subject-item.active .subject-icon svg {
  transform: scale(1.1);
}

.subject-item.active .subject-icon svg path,
.subject-item.active .subject-icon svg circle,
.subject-item.active .subject-icon svg rect {
  stroke: #3F51B5;
  fill: #8A8D9D;
}

.subject-name {
  font-size: 11px;
  font-weight: 400;
  color: #828597;
  text-align: center;
  transition: all 0.3s ease;
  line-height: 1.5;
}

.subject-item.active .subject-name {
  color: #3F51B5;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .subjects-grid {
    gap: 16px;
    max-width: 100%;
  }
  
  .subject-item {
    min-width: 60px;
    padding: 12px 8px;
  }
  
  .subject-icon {
    width: 32px;
    height: 32px;
    margin-bottom: 6px;
  }
  
  .subject-name {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .subjects-grid {
    gap: 12px;
    grid-template-columns: repeat(3, 1fr);
    display: grid;
  }
  
  .subject-item {
    min-width: auto;
  }
}
</style>
