import { appConfig } from '../config/env.js'

const API_BASE = appConfig.api.baseUrl

export async function startMobileUploadSession(memoryId) {
  const res = await fetch(`${API_BASE}/mobile-upload/session/start`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ memoryId })
  })
  if (!res.ok) throw new Error('Failed to start upload session')
  return res.json()
}

export function openUploadSSE(sessionId, onEvent, onError, onClose) {
  const url = `${API_BASE}/mobile-upload/session/stream?sessionId=${encodeURIComponent(sessionId)}`
  const es = new EventSource(url)
  es.onmessage = (evt) => {
    try {
      const data = JSON.parse(evt.data)
      onEvent && onEvent(data)
    } catch (_) {}
  }
  es.onerror = (e) => {
    onError && onError(e)
  }
  es.onopen = () => {}
  es.addEventListener('end', () => {
    onClose && onClose()
  })
  return es
}

export async function getSessionStatus(sessionId) {
  const res = await fetch(`${API_BASE}/mobile-upload/session/status?sessionId=${encodeURIComponent(sessionId)}`)
  if (!res.ok) throw new Error('Failed to fetch session status')
  return res.json()
}

export function buildMobileUploadUrl(token) {
  const base = (import.meta.env.VITE_MOBILE_BASE_URL) || window.location.origin
  return `${base}/m/upload?token=${encodeURIComponent(token)}`
}

