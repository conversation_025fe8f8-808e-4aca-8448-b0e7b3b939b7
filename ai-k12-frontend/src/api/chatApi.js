import axios from 'axios'
import { getApiUrl } from '../config/env.js'

const API_BASE_URL = getApiUrl()

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    console.log('发送请求:', config.url, config.data)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  response => response,
  error => {
    console.error('响应错误:', error)
    return Promise.reject(error)
  }
)

/**
 * K12教育聊天API - 流式对话
 * @param {string} memoryId - 会话ID
 * @param {string} message - 用户消息
 * @param {string} subject - 科目
 * @param {string} grade - 年级
 * @param {Function} onMessage - 消息回调
 * @param {Function} onError - 错误回调
 * @param {Function} onClose - 关闭回调
 * @returns {EventSource} EventSource实例
 */
export function k12ChatStream(memoryId, message, subject, grade, onMessage, onError, onClose) {
  const url = `${API_BASE_URL}/chat/k12/stream`
  const params = new URLSearchParams({
    memoryId: memoryId || '',
    message: message,
    subject: subject || '',
    grade: grade || ''
  })

  const fullUrl = `${url}?${params.toString()}`
  console.log('创建K12聊天流连接:', fullUrl)

  const eventSource = new EventSource(fullUrl)
  let isStreamCompleted = false  // 标记流是否已正常完成

  eventSource.onmessage = function(event) {
    try {
      const data = JSON.parse(event.data)
      if (data.type === 'content') {
        onMessage(data.content)
      } else if (data.type === 'end') {
        console.log('K12聊天流正常结束')
        isStreamCompleted = true  // 标记流已正常完成
        eventSource.close()
        onClose()
      }
    } catch (e) {
      console.error('解析流数据错误:', e)
      onMessage(event.data)
    }
  }

  eventSource.onerror = function(event) {
    console.log('K12聊天流连接状态:', eventSource.readyState)

    // 如果流已经正常完成，不要报告错误
    if (isStreamCompleted) {
      console.log('K12聊天流已正常完成，忽略后续错误事件')
      return
    }

    console.error('K12聊天流错误:', event)
    onError(new Error('K12教育服务连接失败'))
    eventSource.close()
  }

  return eventSource
}

/**
 * 普通聊天API - 兼容原有功能
 * @param {string} memoryId - 会话ID
 * @param {string} message - 用户消息
 * @param {Function} onMessage - 消息回调
 * @param {Function} onError - 错误回调
 * @param {Function} onClose - 关闭回调
 * @returns {EventSource} EventSource实例
 */
export function chatWithSSE(memoryId, message, onMessage, onError, onClose) {
  const url = `${API_BASE_URL}/chat/stream`
  const params = new URLSearchParams({
    memoryId: memoryId || '',
    message: message
  })

  const fullUrl = `${url}?${params.toString()}`
  console.log('创建普通聊天流连接:', fullUrl)

  const eventSource = new EventSource(fullUrl)
  let isStreamCompleted = false  // 标记流是否已正常完成

  eventSource.onmessage = function(event) {
    try {
      const data = JSON.parse(event.data)
      if (data.type === 'content') {
        onMessage(data.content)
      } else if (data.type === 'end') {
        console.log('普通聊天流正常结束')
        isStreamCompleted = true  // 标记流已正常完成
        eventSource.close()
        onClose()
      }
    } catch (e) {
      console.error('解析流数据错误:', e)
      onMessage(event.data)
    }
  }

  eventSource.onerror = function(event) {
    console.log('普通聊天流连接状态:', eventSource.readyState)

    // 如果流已经正常完成，不要报告错误
    if (isStreamCompleted) {
      console.log('普通聊天流已正常完成，忽略后续错误事件')
      return
    }

    console.error('普通聊天流错误:', event)
    onError(new Error('聊天服务连接失败'))
    eventSource.close()
  }

  return eventSource
}

/**
 * 多模态聊天接口 - 支持文本+图片（文件或URL）流式对话
 * @param {string} memoryId - 会话ID
 * @param {string} message - 文本消息
 * @param {File|string|null} image - 图片文件或图片URL（可选）
 * @param {Function} onMessage - 消息回调
 * @param {Function} onError - 错误回调
 * @param {Function} onClose - 关闭回调
 * @returns {EventSource} 一个带有 close 方法的对象
 */
export function multimodalChatStream(memoryId, message, image, onMessage, onError, onClose) {
  // 使用 fetch 发送 multipart/form-data 请求
  const formData = new FormData()
  formData.append('memoryId', memoryId || '')
  formData.append('message', message)
  if (image instanceof File) {
    formData.append('image', image)
  } else if (typeof image === 'string' && image) {
    // 支持直接传入已经在服务器上的图片URL（例如手机扫码上传返回的地址）
    formData.append('imageUrl', image)
  }
  
  // 由于 EventSource 不支持 POST 请求，我们需要使用 fetch + ReadableStream
  console.log('创建多模态聊天流连接:', `${API_BASE_URL}/ai/chat/multimodal`)
  
  let isStreamCompleted = false
  
  fetch(`${API_BASE_URL}/ai/chat/multimodal`, {
    method: 'POST',
    body: formData
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let buffer = ''

    const pump = () => reader.read().then(({ done, value }) => {
      if (done) {
        // 处理收尾残留
        if (buffer.trim()) {
          parseBuffer(buffer, onMessage)
        }
        console.log('多模态聊天流正常结束')
        isStreamCompleted = true
        onClose && onClose()
        return
      }

      buffer += decoder.decode(value, { stream: true })
      // 规范化换行
      buffer = buffer.replace(/\r\n/g, '\n')

      // 以空行作为事件分隔符
      const parts = buffer.split(/\n\n/)
      // 最后一个可能是不完整事件，保留在缓冲区
      buffer = parts.pop() || ''
      for (const evt of parts) {
        const data = extractSseData(evt)
        if (data === null) continue
        if (data === '[DONE]') {
          console.log('多模态聊天流结束标识')
          isStreamCompleted = true
          onClose && onClose()
          return
        }
        onMessage && onMessage(data)
      }

      return pump()
    }).catch(error => {
      if (!isStreamCompleted) {
        console.error('多模态聊天流错误:', error)
        onError && onError(error)
      }
    })

    const extractSseData = (eventChunk) => {
      // 一个事件可能包含多行 data:，需要拼接
      const lines = eventChunk.split('\n')
      let dataLines = []
      for (const line of lines) {
        if (line.startsWith('data:')) {
          // 允许 'data:' 或 'data: '
          dataLines.push(line.replace(/^data:\s?/, ''))
        }
      }
      if (dataLines.length === 0) return null
      return dataLines.join('\n').trim()
    }

    const parseBuffer = (buf, onMsg) => {
      const parts = buf.split(/\n\n/)
      for (const evt of parts) {
        const data = extractSseData(evt)
        if (data && data !== '[DONE]') onMsg && onMsg(data)
      }
    }

    pump()
  })
  .catch(error => {
    if (!isStreamCompleted) {
      console.error('多模态聊天请求失败:', error)
      onError && onError(error)
    }
  })
  
  // 返回一个模拟的 EventSource 对象用于取消
  return {
    close: () => {
      isStreamCompleted = true
    }
  }
}

/**
 * 发送图片消息（废弃，使用 multimodalChatStream 替代）
 * @deprecated 使用 multimodalChatStream 替代
 */
export async function sendImageMessage(memoryId, imageFile, description, subject, grade) {
  try {
    const formData = new FormData()
    formData.append('image', imageFile)
    formData.append('memoryId', memoryId || '')
    formData.append('description', description || '')
    formData.append('subject', subject || '')
    formData.append('grade', grade || '')
    
    const response = await apiClient.post('/chat/k12/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    return response.data
  } catch (error) {
    console.error('发送图片消息失败:', error)
    throw error
  }
}

/**
 * 获取学习建议
 * @param {string} subject - 科目
 * @param {string} grade - 年级
 * @param {string} topic - 主题
 * @returns {Promise}
 */
export async function getStudySuggestions(subject, grade, topic) {
  try {
    const response = await apiClient.get('/chat/k12/suggestions', {
      params: { subject, grade, topic }
    })
    return response.data
  } catch (error) {
    console.error('获取学习建议失败:', error)
    throw error
  }
}

/**
 * 获取练习题
 * @param {string} subject - 科目
 * @param {string} grade - 年级
 * @param {string} concept - 知识点
 * @returns {Promise}
 */
export async function getPracticeQuestions(subject, grade, concept) {
  try {
    const response = await apiClient.get('/chat/k12/practice', {
      params: { subject, grade, concept }
    })
    return response.data
  } catch (error) {
    console.error('获取练习题失败:', error)
    throw error
  }
}

/**
 * 分析学习进度
 * @param {string} memoryId - 会话ID
 * @returns {Promise}
 */
export async function analyzeProgress(memoryId) {
  try {
    const response = await apiClient.get(`/chat/k12/progress/${memoryId}`)
    return response.data
  } catch (error) {
    console.error('分析学习进度失败:', error)
    throw error
  }
}
