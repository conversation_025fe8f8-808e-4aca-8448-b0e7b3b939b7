/**
 * 实时WebSocket语音转录客户端
 * 基于Faster-Whisper流式转录服务
 */
class RealTimeWhisperClient {
  constructor(options = {}) {
    this.wsUrl = options.wsUrl || 'ws://localhost:8765'
    this.language = options.language || 'zh'
    this.onTranscription = options.onTranscription || (() => {})
    this.onError = options.onError || (() => {})
    this.onStatusChange = options.onStatusChange || (() => {})
    this.onDebug = options.onDebug || (() => {})
    this.debug = options.debug || false
    
    // WebSocket相关
    this.ws = null
    this.isConnected = false
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = options.maxReconnectAttempts || 5
    this.reconnectDelay = options.reconnectDelay || 2000
    this.pingInterval = options.pingInterval || 30000
    this.pingTimer = null
    
    // 音频相关
    this.audioContext = null
    this.mediaStream = null
    this.scriptProcessor = null
    this.isRecording = false
    this.sampleRate = options.sampleRate || 16000
    this.bufferSize = options.bufferSize || 4096
    this.sendInterval = options.sendInterval || 500
    this.audioBuffer = []
    this.lastSendTime = 0
    
    // 状态管理
    this.status = 'disconnected'
  }
  
  /**
   * 连接WebSocket服务
   */
  async connect() {
    if (this.isConnected) {
      this.log('debug', '已经连接，跳过重复连接')
      return
    }
    
    try {
      this.log('debug', `连接到 ${this.wsUrl}`)
      this.ws = new WebSocket(this.wsUrl)
      
      this.ws.onopen = () => {
        this.log('debug', 'WebSocket连接成功')
        this.isConnected = true
        this.reconnectAttempts = 0
        this.updateStatus('connected')
        this.startPing()
        
        // 发送语言配置
        this.sendConfig()
      }
      
      this.ws.onmessage = (event) => {
        this.handleMessage(event.data)
      }
      
      this.ws.onclose = (event) => {
        this.log('debug', `WebSocket连接关闭: ${event.code} ${event.reason}`)
        this.isConnected = false
        this.stopPing()
        
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
          this.updateStatus('reconnecting')
          this.scheduleReconnect()
        } else {
          this.updateStatus('disconnected')
        }
      }
      
      this.ws.onerror = (error) => {
        this.log('error', 'WebSocket错误', error)
        this.onError(new Error('WebSocket连接错误'))
      }
      
    } catch (error) {
      this.log('error', '连接失败', error)
      this.onError(error)
    }
  }
  
  /**
   * 断开连接
   */
  disconnect() {
    this.log('debug', '主动断开连接')
    this.stopRecording()
    this.stopPing()
    
    if (this.ws) {
      this.ws.close(1000, '用户主动断开')
      this.ws = null
    }
    
    this.isConnected = false
    this.updateStatus('disconnected')
  }
  
  /**
   * 开始录音
   */
  async startRecording() {
    if (this.isRecording) {
      this.log('debug', '已在录音中')
      return
    }
    
    if (!this.isConnected) {
      await this.connect()
    }
    
    try {
      // 获取麦克风权限
      this.mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.sampleRate,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        }
      })
      
      // 创建音频上下文
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
        sampleRate: this.sampleRate
      })
      
      const source = this.audioContext.createMediaStreamSource(this.mediaStream)
      
      // 创建音频处理器
      this.scriptProcessor = this.audioContext.createScriptProcessor(
        this.bufferSize, 1, 1
      )
      
      this.scriptProcessor.onaudioprocess = (event) => {
        if (this.isRecording) {
          const inputBuffer = event.inputBuffer.getChannelData(0)
          this.processAudioData(inputBuffer)
        }
      }
      
      source.connect(this.scriptProcessor)
      this.scriptProcessor.connect(this.audioContext.destination)
      
      this.isRecording = true
      this.updateStatus('recording')
      this.log('debug', '开始录音')
      
    } catch (error) {
      this.log('error', '启动录音失败', error)
      this.onError(new Error('无法访问麦克风，请检查权限设置'))
    }
  }
  
  /**
   * 停止录音
   */
  stopRecording() {
    if (!this.isRecording) {
      return
    }
    
    this.log('debug', '停止录音')
    this.isRecording = false
    
    // 发送剩余音频数据
    this.flushAudioBuffer()
    
    // 清理音频资源
    if (this.scriptProcessor) {
      this.scriptProcessor.disconnect()
      this.scriptProcessor = null
    }
    
    if (this.audioContext) {
      this.audioContext.close()
      this.audioContext = null
    }
    
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop())
      this.mediaStream = null
    }
    
    this.updateStatus('connected')
  }
  
  /**
   * 处理音频数据
   */
  processAudioData(audioData) {
    // 将Float32Array转换为16位PCM
    const pcmData = new Int16Array(audioData.length)
    for (let i = 0; i < audioData.length; i++) {
      pcmData[i] = Math.max(-32768, Math.min(32767, audioData[i] * 32768))
    }
    
    // 添加到缓冲区
    this.audioBuffer.push(...pcmData)
    
    // 检查是否需要发送
    const now = Date.now()
    if (now - this.lastSendTime >= this.sendInterval) {
      this.flushAudioBuffer()
      this.lastSendTime = now
    }
  }
  
  /**
   * 发送音频缓冲区数据
   */
  flushAudioBuffer() {
    if (this.audioBuffer.length === 0 || !this.isConnected) {
      return
    }
    
    // 转换为Float32Array并编码为Base64
    const float32Data = new Float32Array(this.audioBuffer.length)
    for (let i = 0; i < this.audioBuffer.length; i++) {
      float32Data[i] = this.audioBuffer[i] / 32768.0
    }
    
    const audioBase64 = this.arrayBufferToBase64(float32Data.buffer)
    
    const message = {
      type: 'audio_data',
      audio_data: audioBase64,
      sample_rate: this.sampleRate,
      samples: this.audioBuffer.length
    }
    
    this.sendMessage(message)
    this.log('debug', `发送音频数据: ${this.audioBuffer.length} 样本`)
    
    // 清空缓冲区
    this.audioBuffer = []
  }
  
  /**
   * 发送配置信息
   */
  sendConfig() {
    const config = {
      type: 'config',
      language: this.language,
      task: 'transcribe'
    }
    
    this.sendMessage(config)
    this.log('debug', '发送配置', config)
  }
  
  /**
   * 发送消息到WebSocket
   */
  sendMessage(message) {
    if (this.ws && this.isConnected) {
      this.ws.send(JSON.stringify(message))
    }
  }
  
  /**
   * 处理接收到的消息
   */
  handleMessage(data) {
    try {
      const message = JSON.parse(data)
      this.log('debug', '收到消息', message)
      
      switch (message.type) {
        case 'connected':
          this.log('debug', '服务器确认连接')
          break
          
        case 'config_updated':
          this.log('debug', '配置已更新', message.config)
          break
          
        case 'audio_received':
          this.log('debug', `音频已接收: 缓冲区大小 ${message.buffer_size}`)
          break
          
        case 'transcription':
          this.log('debug', '收到转录结果', message.data)
          this.onTranscription(message.data)
          break
          
        case 'error':
          this.log('error', '服务器错误', message.message)
          this.onError(new Error(message.message))
          break
          
        case 'pong':
          this.log('debug', '收到心跳响应')
          break
          
        default:
          this.log('debug', '未知消息类型', message)
      }
    } catch (error) {
      this.log('error', '解析消息失败', error)
    }
  }
  
  /**
   * 开始心跳检测
   */
  startPing() {
    this.stopPing()
    this.pingTimer = setInterval(() => {
      if (this.isConnected) {
        this.sendMessage({ type: 'ping' })
      }
    }, this.pingInterval)
  }
  
  /**
   * 停止心跳检测
   */
  stopPing() {
    if (this.pingTimer) {
      clearInterval(this.pingTimer)
      this.pingTimer = null
    }
  }
  
  /**
   * 安排重连
   */
  scheduleReconnect() {
    this.reconnectAttempts++
    const delay = this.reconnectDelay * this.reconnectAttempts
    
    this.log('debug', `${delay}ms后尝试第${this.reconnectAttempts}次重连`)
    
    setTimeout(() => {
      if (!this.isConnected) {
        this.connect()
      }
    }, delay)
  }
  
  /**
   * 更新状态
   */
  updateStatus(newStatus) {
    if (this.status !== newStatus) {
      this.status = newStatus
      this.onStatusChange(newStatus)
      this.log('debug', `状态变更: ${newStatus}`)
    }
  }
  
  /**
   * ArrayBuffer转Base64
   */
  arrayBufferToBase64(buffer) {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  }
  
  /**
   * 日志输出
   */
  log(level, message, data = null) {
    if (this.debug) {
      const timestamp = new Date().toISOString()
      console.log(`[${timestamp}] [RealTimeWhisperClient] [${level.toUpperCase()}] ${message}`, data || '')
    }
    
    if (this.onDebug) {
      this.onDebug(level, message, data)
    }
  }
  
  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      status: this.status,
      isConnected: this.isConnected,
      isRecording: this.isRecording,
      reconnectAttempts: this.reconnectAttempts
    }
  }
}

export default RealTimeWhisperClient
