// 简单的 Float32 PCM -> 16-bit PCM WAV 编码器
// 说明：前端常见做法是采集 Web Audio PCM，再编码为 WAV（16kHz/16bit/mono）

function downsampleBuffer(buffer, inSampleRate, outSampleRate) {
  if (outSampleRate === inSampleRate) {
    return buffer
  }
  if (outSampleRate > inSampleRate) {
    // 不上采样，直接返回
    return buffer
  }
  const ratio = inSampleRate / outSampleRate
  const newLen = Math.floor(buffer.length / ratio)
  const result = new Float32Array(newLen)
  let offsetResult = 0
  let offsetBuffer = 0
  while (offsetResult < result.length) {
    const nextOffsetBuffer = Math.round((offsetResult + 1) * ratio)
    // 简单平均避免明显混叠
    let accum = 0, count = 0
    for (let i = offsetBuffer; i < nextOffsetBuffer && i < buffer.length; i++) {
      accum += buffer[i]
      count++
    }
    result[offsetResult] = accum / (count || 1)
    offsetResult++
    offsetBuffer = nextOffsetBuffer
  }
  return result
}

function floatTo16BitPCM(float32Array) {
  const buffer = new ArrayBuffer(float32Array.length * 2)
  const view = new DataView(buffer)
  let offset = 0
  for (let i = 0; i < float32Array.length; i++, offset += 2) {
    let s = Math.max(-1, Math.min(1, float32Array[i]))
    view.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7FFF, true)
  }
  return view
}

export function encodeWAV(float32Array, inSampleRate, outSampleRate = 16000) {
  const mono = downsampleBuffer(float32Array, inSampleRate, outSampleRate)
  const pcm16 = floatTo16BitPCM(mono)

  const headerSize = 44
  const dataSize = pcm16.byteLength
  const buffer = new ArrayBuffer(headerSize + dataSize)
  const view = new DataView(buffer)

  // RIFF identifier 'RIFF'
  writeString(view, 0, 'RIFF')
  // RIFF chunk length
  view.setUint32(4, 36 + dataSize, true)
  // RIFF type 'WAVE'
  writeString(view, 8, 'WAVE')
  // format chunk identifier 'fmt '
  writeString(view, 12, 'fmt ')
  // format chunk length
  view.setUint32(16, 16, true)
  // sample format (raw)
  view.setUint16(20, 1, true)
  // channel count
  view.setUint16(22, 1, true)
  // sample rate
  view.setUint32(24, outSampleRate, true)
  // byte rate (sample rate * block align)
  view.setUint32(28, outSampleRate * 2, true)
  // block align (channel count * bytes per sample)
  view.setUint16(32, 2, true)
  // bits per sample
  view.setUint16(34, 16, true)
  // data chunk identifier 'data'
  writeString(view, 36, 'data')
  // data chunk length
  view.setUint32(40, dataSize, true)

  // PCM data
  const pcmBytes = new Uint8Array(buffer, headerSize)
  pcmBytes.set(new Uint8Array(pcm16.buffer))

  return new Uint8Array(buffer)
}

function writeString(view, offset, string) {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i))
  }
}

