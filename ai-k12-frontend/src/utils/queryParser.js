/**
 * 智能查询解析器
 * 从用户输入中提取年级和学科信息
 */

// 年级映射表
const GRADE_MAPPING = {
  // 小学年级
  '小学一年级': 'P1',
  '小学二年级': 'P2',
  '小学三年级': 'P3',
  '小学四年级': 'P4',
  '小学五年级': 'P5',
  '小学六年级': 'P6',
  '一年级': 'P1',
  '二年级': 'P2',
  '三年级': 'P3',
  '四年级': 'P4',
  '五年级': 'P5',
  '六年级': 'P6',
  '初一': 'P6',
  '初二': 'secondary',
  '初三': 'secondary',
  
  // 数字形式
  '1年级': 'P1',
  '2年级': 'P2',
  '3年级': 'P3',
  '4年级': 'P4',
  '5年级': 'P5',
  '6年级': 'P6',
  '7年级': 'secondary',
  '8年级': 'secondary',
  '9年级': 'secondary'
};

// 学科关键词映射
const SUBJECT_KEYWORDS = {
  '数学': 'Math',
  '语文': 'Chinese',
  '英语': 'English',
  '科学': 'Science',
  '物理': 'Physics',
  '化学': 'Chemistry'
};

/**
 * 智能解析查询中的年级和学科信息
 * @param {string} query 用户查询
 * @returns {object} 包含grade和subject的对象
 */
export function parseQuery(query) {
  if (!query) return { grade: null, subject: null };
  
  const result = {
    grade: null,
    subject: null
  };
  
  // 查找年级
  for (const [key, value] of Object.entries(GRADE_MAPPING)) {
    if (query.includes(key)) {
      result.grade = value;
      break;
    }
  }
  
  // 如果没找到，尝试正则匹配
  if (!result.grade) {
    const gradeRegex = /(小学|初|高)?(一|二|三|四|五|六|七|八|九|1|2|3|4|5|6|7|8|9)年级?/;
    const match = query.match(gradeRegex);
    if (match) {
      const gradeText = match[0];
      result.grade = GRADE_MAPPING[gradeText] || null;
    }
  }
  
  // 查找学科
  for (const [key, value] of Object.entries(SUBJECT_KEYWORDS)) {
    if (query.includes(key)) {
      result.subject = value;
      break;
    }
  }
  
  return result;
}

/**
 * 增强查询，添加年级学科信息提示
 * @param {string} query 原始查询
 * @param {object} parsed 已解析的信息
 * @returns {string} 增强后的查询
 */
export function enhanceQuery(query, parsed) {
  if (!parsed.grade && !parsed.subject) return query;
  
  let enhanced = query;
  if (parsed.grade) {
    enhanced += ` [年级:${parsed.grade}]`;
  }
  if (parsed.subject) {
    enhanced += ` [学科:${parsed.subject}]`;
  }
  
  return enhanced;
}

export default {
  parseQuery,
  enhanceQuery
};