import { createRouter, createWebHistory } from 'vue-router'
import Home from '../components/Home.vue'
import VoiceTest from '../components/VoiceTest.vue'
import StreamTest from '../components/StreamTest.vue'
import MobileUpload from '../components/mobile/MobileUpload.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/m/upload',
    name: 'MobileUpload',
    component: MobileUpload
  },
  {
    path: '/test',
    name: 'VoiceTest',
    component: VoiceTest
  },
  {
    path: '/stream',
    name: 'StreamTest',
    component: StreamTest
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
