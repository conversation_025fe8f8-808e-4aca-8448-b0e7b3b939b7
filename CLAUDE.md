# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an AI programming assistant project based on Spring Boot, Vue.js, and LangChain4j. The application provides programming learning guidance, interview preparation assistance, and technical Q&A through both general chat and RAG-enhanced knowledge base queries.

### Key Technologies
- **Backend**: Java 21, Spring Boot 3.5.3, LangChain4j 1.1.0, <PERSON>ven
- **Frontend**: Vue.js 3.3.4, Vite 4.4.9, SSE for real-time streaming
- **AI**: Google Gemini API (gemini-2.5-flash-lite), text-embedding-004, local embedding models
- **Database**: PostgreSQL with PGVector for vector storage
- **Architecture**: RESTful API + SSE streaming, RAG (Retrieval Augmented Generation)

## Quick Start Commands

### Backend
```bash
# From project root
./mvnw spring-boot:run           # Start backend service
./mvnw package                   # Build project
./mvnw test                      # Run tests
./mvnw test -Dtest=RagServiceTest # Run specific RAG tests
```

### Frontend
```bash
cd ai-code-helper-frontend
npm install                      # Install dependencies
npm run dev                      # Start dev server (port 5173)
npm run build                    # Build for production
npm run preview                  # Preview production build
```

## Architecture Overview

### Backend Structure
```
src/main/java/com/hujun/aicodehelper/
├── AiCodeHelperApplication.java          # Main entry point
├── controller/
│   └── AiController.java                 # REST/SSE endpoints
├── ai/
│   ├── AiCodeHelperService.java          # Core AI service interface
│   ├── RagService.java                   # RAG knowledge base service
│   ├── guardrail/SafeInputGuardrail.java # Input validation
│   ├── model/                           # AI model configurations
│   │   ├── GeminiChatModelConfig.java   # Google Gemini setup
│   │   ├── LocalEmbeddingModel.java     # Local embedding support
│   │   └── PostgresVectorStore.java     # Vector storage
│   ├── rag/RagConfig.java               # RAG configuration
│   └── tools/InterviewQuestionTool.java # Web scraping tool
└── config/CorsConfig.java               # CORS configuration
```

### API Endpoints

#### General Chat
- `GET /api/ai/chat?memoryId=123&message=hello` - SSE streaming chat

#### RAG Enhanced Features
- `POST /api/ai/rag/chat` - Sync RAG Q&A with knowledge sources
- `GET /api/ai/rag/chat-stream` - SSE streaming RAG chat
- `POST /api/ai/rag/search` - Knowledge base search
- `GET /api/ai/rag/stats` - Knowledge base statistics

### Frontend Structure
```
ai-code-helper-frontend/
├── src/
│   ├── api/
│   │   ├── chatApi.js      # General chat API
│   │   └── ragApi.js       # RAG-specific API
│   ├── components/
│   │   ├── ChatMessage.vue    # Standard chat messages
│   │   ├── RagMessage.vue     # RAG-enhanced messages with sources
│   │   ├── ChatModeToggle.vue # Mode switching UI
│   │   ├── ChatInput.vue      # Message input
│   │   └── LoadingDots.vue    # Loading animation
│   ├── App.vue            # Main application
│   └── main.js            # Entry point
```

## Configuration

### Required Setup
1. **API Keys**: Configure in `src/main/resources/application.yml`
   - Google AI API key for Gemini models
   - PostgreSQL connection (port 5433 by default)

2. **Environment**: Ensure PostgreSQL with PGVector extension is running

### Key Configuration Files
- `application.yml` - Database and AI service configuration
- `system-prompt.txt` - AI system prompt for behavior control
- Knowledge base documents in `src/main/resources/docs/` (4 MD files)

## Development Workflow

### Adding New Features
1. **Backend**: Add new endpoints in `AiController.java`
2. **RAG Enhancement**: Extend `RagService.java` for new knowledge sources
3. **Frontend**: Create new Vue components in `src/components/`
4. **Testing**: Use `./mvnw test` to validate changes

### Knowledge Base Updates
- Add new `.md` files to `src/main/resources/docs/`
- Restart backend to trigger re-indexing
- Use `/api/ai/rag/stats` to verify document count

### Common Development Tasks
- **Debug RAG**: Check `/api/ai/rag/stats` and browser console logs
- **Test API**: Use curl commands from RAG_API_GUIDE.md
- **Frontend Changes**: Hot reload with `npm run dev`
- **Database Issues**: Verify PostgreSQL connection and PGVector extension

## Service URLs
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:8081/api
- **Database**: *******************************************

## Key Features
- **Dual Mode Interface**: Switch between general chat and knowledge-based Q&A
- **RAG Integration**: Real-time knowledge retrieval with source attribution
- **Stream Processing**: Real-time AI responses via Server-Sent Events
- **Responsive Design**: Mobile-optimized Vue.js interface
- **Markdown Support**: Full markdown rendering with code highlighting