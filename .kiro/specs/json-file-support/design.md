# 设计文档

## 概述

本设计扩展现有的RAG系统以支持JSON文件处理。系统将保持现有的Markdown文件处理功能，同时添加JSON文件的自动发现、解析、文本提取和向量化功能。设计采用插件化架构，确保JSON处理与现有MD处理逻辑解耦，便于维护和扩展。

核心设计原则：
- 保持现有MD文件处理功能不变
- 采用统一的文档处理接口
- 支持多种JSON结构的灵活处理
- 提供完整的配置和监控能力
- 确保高性能和内存效率

## 架构

### 整体架构图

```mermaid
graph TB
    A[RagConfig] --> B[DocumentLoaderFactory]
    B --> C[MarkdownDocumentLoader]
    B --> D[JsonDocumentLoader]
    
    D --> E[JsonFileScanner]
    D --> F[JsonContentExtractor]
    D --> G[JsonTextTransformer]
    
    C --> H[Document List]
    D --> H
    H --> I[EmbeddingStoreIngestor]
    I --> J[Vector Database]
    
    K[Configuration] --> L[JsonProcessingConfig]
    L --> D
    
    M[Logging] --> N[JsonProcessingLogger]
    N --> D
```

### 核心组件关系

1. **DocumentLoaderFactory**: 工厂类，根据配置决定加载哪些类型的文档
2. **JsonDocumentLoader**: JSON文档加载器，负责整个JSON处理流程
3. **JsonFileScanner**: 文件扫描器，递归发现JSON文件
4. **JsonContentExtractor**: 内容提取器，从JSON结构中提取文本
5. **JsonTextTransformer**: 文本转换器，将提取的内容转换为Document对象

## 组件和接口

### 1. 配置组件

#### JsonProcessingConfig
```java
@ConfigurationProperties(prefix = "rag.json")
public class JsonProcessingConfig {
    private boolean enabled = true;
    private List<String> sourcePaths = new ArrayList<>();
    private List<String> includePatterns = Arrays.asList("**/*.json");
    private List<String> excludePatterns = new ArrayList<>();
    private int maxFileSize = 10 * 1024 * 1024; // 10MB
    private int processingTimeout = 300; // 5分钟
}
```

#### 配置属性说明
- `rag.json.enabled`: 是否启用JSON处理
- `rag.json.source-paths`: JSON文件源目录列表
- `rag.json.include-patterns`: 包含文件模式
- `rag.json.exclude-patterns`: 排除文件模式
- `rag.json.max-file-size`: 最大文件大小限制
- `rag.json.processing-timeout`: 处理超时时间

### 2. 文档加载组件

#### DocumentLoaderFactory
```java
@Component
public class DocumentLoaderFactory {
    public List<Document> loadAllDocuments();
    private List<Document> loadMarkdownDocuments();
    private List<Document> loadJsonDocuments();
}
```

#### JsonDocumentLoader
```java
@Component
public class JsonDocumentLoader {
    public List<Document> loadDocuments(List<String> sourcePaths);
    private List<Path> scanJsonFiles(String sourcePath);
    private Document processJsonFile(Path filePath);
}
```

### 3. JSON处理组件

#### JsonFileScanner
```java
@Component
public class JsonFileScanner {
    public List<Path> scanFiles(String rootPath, List<String> includePatterns, List<String> excludePatterns);
    private boolean matchesPattern(Path file, List<String> patterns);
}
```

#### JsonContentExtractor
```java
@Component
public class JsonContentExtractor {
    public String extractText(JsonNode jsonNode, String fileName);
    private String extractCambridgeExamContent(JsonNode node);
    private String extractGenericJsonContent(JsonNode node);
    private String flattenJsonStructure(JsonNode node, String prefix);
}
```

#### JsonTextTransformer
```java
@Component
public class JsonTextTransformer {
    public Document transformToDocument(String extractedText, Path filePath, Map<String, Object> metadata);
    private Map<String, Object> buildMetadata(Path filePath, JsonNode originalJson);
}
```

### 4. 监控和日志组件

#### JsonProcessingLogger
```java
@Component
public class JsonProcessingLogger {
    public void logProcessingStart(int directoryCount, int fileCount);
    public void logFileProcessing(String fileName, ProcessingStatus status);
    public void logProcessingComplete(ProcessingSummary summary);
    public void logError(String filePath, Exception error);
}
```

## 数据模型

### JSON内容提取模型

#### CambridgeExamContent
```java
public class CambridgeExamContent {
    private String activityTitle;
    private String instructions;
    private String passage;
    private List<QuestionGap> gaps;
    private String questionType;
}
```

#### ProcessingSummary
```java
public class ProcessingSummary {
    private int totalFiles;
    private int processedFiles;
    private int failedFiles;
    private int createdSegments;
    private long processingTimeMs;
    private List<String> errorFiles;
}
```

### 元数据结构

JSON文档的元数据将包含：
- `file_name`: 文件名
- `file_path`: 完整文件路径
- `file_type`: "json"
- `source_directory`: 源目录
- `content_type`: JSON内容类型（如"cambridge_exam"）
- `activity_type`: 活动类型（如"Reading and Use of English"）
- `part_number`: 部分编号
- `activity_number`: 活动编号
- `processing_timestamp`: 处理时间戳

## 错误处理

### 错误类型和处理策略

1. **文件访问错误**
   - 文件不存在：记录警告，跳过文件
   - 权限不足：记录错误，跳过文件
   - 文件过大：记录警告，跳过文件

2. **JSON解析错误**
   - 格式错误：记录错误详情，跳过文件
   - 编码问题：尝试多种编码，失败则跳过

3. **内容提取错误**
   - 空内容：记录警告，跳过文件
   - 结构异常：使用通用提取器，记录警告

4. **向量化错误**
   - 嵌入失败：记录错误，跳过该文档
   - 存储失败：记录错误，继续处理其他文档

### 错误恢复机制

- 单个文件处理失败不影响其他文件
- 提供重试机制（可配置重试次数）
- 支持跳过问题文件继续处理
- 详细的错误日志便于问题排查

## 测试策略

### 单元测试

1. **JsonContentExtractor测试**
   - 测试剑桥考试JSON结构提取
   - 测试通用JSON结构提取
   - 测试空内容和异常结构处理

2. **JsonFileScanner测试**
   - 测试文件模式匹配
   - 测试递归目录扫描
   - 测试文件过滤功能

3. **JsonTextTransformer测试**
   - 测试Document对象创建
   - 测试元数据构建
   - 测试文本格式化

### 集成测试

1. **端到端JSON处理测试**
   - 使用示例JSON文件测试完整流程
   - 验证向量化和检索功能
   - 测试配置变更的影响

2. **性能测试**
   - 大量JSON文件处理性能
   - 内存使用情况监控
   - 并发处理能力测试

3. **错误场景测试**
   - 损坏的JSON文件处理
   - 网络中断场景
   - 磁盘空间不足场景

### 测试数据

- 使用现有的剑桥考试JSON文件作为测试数据
- 创建各种异常情况的测试JSON文件
- 准备大规模测试数据集验证性能

## 性能考虑

### 内存优化

1. **流式处理**
   - 使用Jackson Streaming API处理大型JSON文件
   - 避免将整个文件加载到内存
   - 分批处理文档列表

2. **资源管理**
   - 及时关闭文件流和资源
   - 使用try-with-resources确保资源释放
   - 定期触发垃圾回收

### 处理效率

1. **并行处理**
   - 支持多线程处理多个JSON文件
   - 使用CompletableFuture异步处理
   - 可配置线程池大小

2. **缓存策略**
   - 缓存文件扫描结果
   - 缓存JSON解析结果
   - 避免重复处理相同文件

### 监控指标

- 处理文件数量和速度
- 内存使用峰值
- 错误率统计
- 向量化耗时分析

## 配置集成

### application.yml配置示例

```yaml
rag:
  json:
    enabled: true
    source-paths:
      - "data/cambridge/2025-06-28/output_json_standalone"
      - "data/other-json-sources"
    include-patterns:
      - "**/*.json"
    exclude-patterns:
      - "**/temp/**"
      - "**/.DS_Store"
    max-file-size: 10485760  # 10MB
    processing-timeout: 300   # 5分钟
    parallel-processing: true
    thread-pool-size: 4
```

### 配置验证

- 启动时验证配置参数有效性
- 检查源路径是否存在和可访问
- 验证文件模式语法正确性
- 提供配置错误的详细提示信息