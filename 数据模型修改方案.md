 **通用数学题库 JSON Schema（适合 RAG + 数学专用处理）**。
 这个 Schema 兼顾了：**检索、展示、计算、图形关联**，未来扩展性也比较好。

------

## 📐 推荐 JSON Schema

```
{
  "id": "string",                 // 唯一ID（如 psle_2020_p1_q5）
  "type": "concept | example | problem | solution | definition", 
  "content": "string",            // 题干或概念正文
  "answer": "string | null",      // 答案或解答摘要
  "latex": "string | null",       // LaTeX 公式（便于渲染）
  "sympy_expr": "string | null",  // 可计算表达式（便于验证答案）
  "graph": "string | null",       // 关联图形文件路径或SVG
  "topic": "string",              // 主题（如 Algebra / Geometry）
  "grade": "string",              // 年级/水平（如 P5, Sec2）
  "difficulty": "基础 | 中等 | 挑战",
  "source": "string",             // 来源（试卷/教材/练习）
  "metadata": {
    "chapter": "string | null",   // 章节
    "page": "int | null",         // 页码
    "tags": ["string"]            // 额外标签，方便检索
  }
}
```

------

## 📘 示例 1：代数题

```
{
  "id": "psle_2020_p1_q5",
  "type": "problem",
  "content": "Solve 2x + 3 = 7",
  "answer": "x = 2",
  "latex": "2x + 3 = 7 \\\\Rightarrow x = 2",
  "sympy_expr": "Eq(2*x + 3, 7)",
  "graph": null,
  "topic": "Algebra",
  "grade": "Sec2",
  "difficulty": "基础",
  "source": "PSLE 2020 Paper 1 Q5",
  "metadata": {
    "chapter": "Linear Equations",
    "page": 12,
    "tags": ["equation", "solving"]
  }
}
```

------

## 📘 示例 2：几何题（带图形）

```
{
  "id": "psle_2021_p2_q12",
  "type": "problem",
  "content": "Find the area of the shaded triangle in rectangle ABCD, where AE = 4 cm, EB = 8 cm, and DC = 6 cm.",
  "answer": "36 cm^2",
  "latex": "Area = \\tfrac{1}{2} \\times (AE + EB) \\times DC = 36 \\, cm^2",
  "sympy_expr": "Rational(1,2) * (4+8) * 6",
  "graph": "triangle_area_2021_q12.svg",
  "topic": "Geometry",
  "grade": "P5",
  "difficulty": "中等",
  "source": "PSLE 2021 Paper 2 Q12",
  "metadata": {
    "chapter": "Triangle Area",
    "page": 45,
    "tags": ["triangle", "area"]
  }
}
```

------

## 📘 示例 3：概念定义（无答案）

```
{
  "id": "concept_triangle_height",
  "type": "concept",
  "content": "Height of a triangle is the perpendicular distance from a vertex to the opposite side.",
  "answer": null,
  "latex": null,
  "sympy_expr": null,
  "graph": "triangle_height.svg",
  "topic": "Geometry",
  "grade": "P5",
  "difficulty": "基础",
  "source": "Textbook P5 Chapter 3",
  "metadata": {
    "chapter": "Triangle Properties",
    "page": 10,
    "tags": ["definition", "triangle"]
  }
}
```

------

⚡这样设计的好处：

- **可检索**（text/topic/grade/source）
- **可渲染**（latex → 前端公式展示，graph → 图形）
- **可计算**（sympy_expr → 自动算答案，校验 correctness）
- **可扩展**（tags/metadata 方便以后加多语言、知识点映射）





## 修改后的表结构（pgvector）

```
CREATE TABLE math_rag_knowledge (
    id SERIAL PRIMARY KEY,
    source TEXT,                -- 来源（PDF 名称/试卷 URL 等）
    type TEXT,                  -- 内容类型：problem/concept/example/definition
    content TEXT,               -- 主体文本（题干/定义/讲解）
    answer TEXT,                -- 答案（如果有）
    latex TEXT,                 -- LaTeX 表达式（用于渲染）
    sympy_expr TEXT,            -- 可计算的表达式（用于验证）
    graph TEXT,                 -- 图形文件路径或SVG
    grade TEXT,                 -- 年级（P5, Sec2, O-Level…）
    topic TEXT,                 -- 知识点（Algebra, Geometry, etc.）
    difficulty TEXT,            -- 难度：基础/中等/挑战
    metadata JSONB,             -- 额外信息（章节/页码/年份/标签）
    embedding vector(1536)      -- 向量表示（用于RAG检索）
);
```

------

## 📘 优势

1. **content/answer 分离** → 查询时可选择只检索题干或答案
2. **latex / sympy_expr** → 方便做渲染和自动验证
3. **graph** → 明确绑定图形与题目
4. **metadata** → 保持灵活，可以随时扩展章节、页码、标签
5. **embedding** → 依旧是核心检索字段

------

## 📌 补充说明

- 对于不含公式/图形的记录，`latex/sympy_expr/graph` 可以存 `NULL`
- 你原有的数据结构也能兼容迁移过来，只是扩展了几个字段
- 如果以后要做多语言（比如中英文对照），可以考虑再加 `lang` 字段