# Repository Guidelines

## Project Structure & Module Organization
- Backend: `src/main/java` (Spring Boot), resources in `src/main/resources`; tests in `src/test/java`.
- Frontend: `ai-k12-frontend/` (Vue 3 + Vite). Static assets in `public/`, code in `src/`.
- Data & Docs: `data/` (sample JSON/knowledge), `doc/` (design/notes).
- Entrypoints: `com.hujun.aicodehelper.AiCodeHelperApplication` (main API) and `com.hujun.pdfparser.MarkdownApplication` (markdown-only mode).

## Build, Test, and Development Commands
- Backend dev: `mvn spring-boot:run`
  - Profiles: `-Dspring-boot.run.profiles=postgresql` or `markdown`.
  - Alternate main: `-Dspring-boot.run.main-class=com.hujun.pdfparser.MarkdownApplication`.
- Backend build: `mvn clean package` (outputs `target/*.jar`).
- Backend tests: `mvn test` (JUnit 5 + Spring Boot Test).
- Frontend dev: `cd ai-k12-frontend && npm install && npm run dev`.
- Frontend build: `cd ai-k12-frontend && npm run build` (dist in `ai-k12-frontend/dist`).

## Coding Style & Naming Conventions
- Java: 4-space indentation; classes `PascalCase`; methods/fields `camelCase`; constants `UPPER_SNAKE_CASE`.
- Packages under `com.hujun.aicodehelper` or `com.hujun.pdfparser`. Keep components cohesive by layer (controller/service/config/model).
- Vue: single-file components in `src/components` as `PascalCase.vue`; utilities in `src/utils` as `camelCase.js`.
- Formatting: keep Lombok annotations minimal and consistent; prefer constructor injection for services.

## Testing Guidelines
- Frameworks: JUnit 5 and Spring Boot Test.
- Location & naming: mirror package structure under `src/test/java`; suffix tests with `*Test.java` (e.g., `RagConfigJsonTest`).
- Scope: unit tests for pure logic (JSON processing, string utils) and slice/integration tests for Spring beans where helpful.
- Run: `mvn test`. Aim for fast, deterministic tests; avoid external network calls.

## Commit & Pull Request Guidelines
- Commits: clear, action-oriented subject line; prefer present tense. Keep changes scoped; split refactors from behavior changes.
- Branches: `feature/<short-name>`, `fix/<issue-id>-<short-name>`.
- PRs: include purpose, key changes, testing notes, and screenshots or API samples where relevant. Link related issues. Ensure `mvn test` and frontend build pass locally.

## Security & Configuration Tips
- Do not commit secrets. Backend keys go in Spring profiles (e.g., `application-postgresql.yml`); frontend uses `.env` (`VITE_*`).
- PostgreSQL + pgvector required for RAG features. Verify `CREATE EXTENSION IF NOT EXISTS vector;` before enabling `postgresql` profile.
