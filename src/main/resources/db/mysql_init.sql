-- MySQL 聊天记忆数据库初始化脚本

-- 创建聊天消息表
CREATE TABLE IF NOT EXISTS chat_messages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    memory_id VARCHAR(255) NOT NULL COMMENT '内存ID，用于区分不同的聊天会话',
    message_order INT NOT NULL COMMENT '消息在对话中的顺序',
    message_type VARCHAR(50) NOT NULL COMMENT '消息类型',
    message_content TEXT NOT NULL COMMENT '消息内容（JSON格式）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天消息表';

-- 创建索引
CREATE INDEX idx_memory_id ON chat_messages(memory_id);
CREATE INDEX idx_memory_id_order ON chat_messages(memory_id, message_order);

-- 插入示例数据（可选）
INSERT INTO chat_messages (memory_id, message_order, message_type, message_content) VALUES
('demo_session_1', 0, 'USER', '{"type":"USER","text":"你好，请介绍一下Java的特点"}'),
('demo_session_1', 1, 'AI', '{"type":"AI","text":"Java是一种面向对象的编程语言，具有以下特点：\\n1. 跨平台性：一次编译，到处运行\\n2. 面向对象：支持封装、继承、多态\\n3. 安全性：内置安全机制\\n4. 多线程：原生支持并发编程\\n5. 自动内存管理：垃圾回收机制"}'),
('demo_session_1', 2, 'USER', '{"type":"USER","text":"能详细解释一下Java的垃圾回收机制吗？"}'),
('demo_session_1', 3, 'AI', '{"type":"AI","text":"Java的垃圾回收（GC）机制是自动内存管理的核心特性：\\n\\n**工作原理：**\\n1. 自动检测不再被引用的对象\\n2. 释放这些对象占用的内存空间\\n3. 整理内存碎片\\n\\n**主要算法：**\\n- 标记-清除（Mark-Sweep）\\n- 复制算法（Copying）\\n- 标记-整理（Mark-Compact）\\n- 分代收集（Generational）\\n\\n**优点：**\\n- 避免内存泄漏\\n- 减少程序员负担\\n- 提高程序安全性\\n\\n**注意：**\\nGC虽然自动，但不当的编程可能影响性能，需要合理设计对象生命周期。"}');

-- 显示表结构
DESCRIBE chat_messages;