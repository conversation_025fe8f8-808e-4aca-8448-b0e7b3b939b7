-- 重建embeddings表以支持新的向量维度
-- 用于解决向量维度不匹配问题

-- 1. 删除现有表和相关对象
DROP TABLE IF EXISTS embeddings CASCADE;
DROP FUNCTION IF EXISTS search_embeddings CASCADE;
DROP FUNCTION IF EXISTS add_embedding CASCADE;

-- 2. 创建 pgvector 扩展（如果还没有安装）
CREATE EXTENSION IF NOT EXISTS vector;

-- 3. 创建新的向量存储表（3072维向量，支持gemini-embedding-001）
CREATE TABLE IF NOT EXISTS embeddings (
    id SERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB,
    embedding vector(3072), -- gemini-embedding-001 的向量维度是 3072
    file_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. 创建向量相似度搜索索引
-- 使用 IVFFlat 索引提高查询性能
CREATE INDEX IF NOT EXISTS embeddings_embedding_idx 
ON embeddings USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100);

-- 5. 创建元数据索引
CREATE INDEX IF NOT EXISTS embeddings_metadata_idx 
ON embeddings USING GIN (metadata);

-- 6. 创建文件名索引
CREATE INDEX IF NOT EXISTS embeddings_file_name_idx 
ON embeddings (file_name);

-- 7. 创建相似度搜索函数（3072维）
CREATE OR REPLACE FUNCTION search_embeddings(
    query_embedding vector(3072),
    match_threshold float DEFAULT 0.75,
    match_count int DEFAULT 5
)
RETURNS TABLE(
    id int,
    content text,
    metadata jsonb,
    file_name varchar(255),
    similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        e.id,
        e.content,
        e.metadata,
        e.file_name,
        1 - (e.embedding <=> query_embedding) AS similarity
    FROM embeddings e
    WHERE 1 - (e.embedding <=> query_embedding) > match_threshold
    ORDER BY e.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- 8. 创建添加向量的函数（3072维）
CREATE OR REPLACE FUNCTION add_embedding(
    p_content text,
    p_metadata jsonb,
    p_embedding vector(3072),
    p_file_name varchar(255)
)
RETURNS int
LANGUAGE plpgsql
AS $$
DECLARE
    new_id int;
BEGIN
    INSERT INTO embeddings (content, metadata, embedding, file_name)
    VALUES (p_content, p_metadata, p_embedding, p_file_name)
    RETURNING id INTO new_id;
    
    RETURN new_id;
END;
$$;

-- 完成提示
SELECT '🎉 embeddings表已重建完成！现在支持3072维向量 (gemini-embedding-001)' as status;