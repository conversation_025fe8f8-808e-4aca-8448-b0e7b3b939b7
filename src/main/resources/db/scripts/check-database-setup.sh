#!/bin/bash

# 单一数据库配置检查脚本
# 用于验证PostgreSQL配置是否正确

echo "🔍 开始检查单一数据库配置..."
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查函数
check_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
        return 0
    else
        echo -e "${RED}❌ $2${NC}"
        return 1
    fi
}

# 1. 检查PostgreSQL连接
echo -e "\n${YELLOW}🐘 检查PostgreSQL连接...${NC}"
PGPASSWORD="mypassword" psql -h 8.137.78.144 -p 5433 -U myappuser -d postgresml -c "SELECT 1;" 2>/dev/null
check_status $? "PostgreSQL连接正常"

# 2. 检查pgvector扩展
echo -e "\n${YELLOW}🔧 检查pgvector扩展...${NC}"
PGPASSWORD="mypassword" psql -h 8.137.78.144 -p 5433 -U myappuser -d postgresml -c "SELECT * FROM pg_extension WHERE extname = 'vector';" 2>/dev/null | grep -q "vector"
check_status $? "pgvector扩展已安装"

# 3. 检查embeddings表
echo -e "\n${YELLOW}📋 检查embeddings表...${NC}"
PGPASSWORD="mypassword" psql -h 8.137.78.144 -p 5433 -U myappuser -d postgresml -c "\d embeddings" 2>/dev/null | grep -q "Table"
check_status $? "embeddings表存在"

# 4. 检查chat_messages表
echo -e "\n${YELLOW}💬 检查chat_messages表...${NC}"
PGPASSWORD="mypassword" psql -h 8.137.78.144 -p 5433 -U myappuser -d postgresml -c "\d chat_messages" 2>/dev/null | grep -q "Table"
check_status $? "chat_messages表存在"

# 5. 检查Java配置文件
echo -e "\n${YELLOW}☕ 检查Java配置文件...${NC}"

# 检查主配置文件
if grep -q "postgresql" src/main/resources/application.yml; then
    echo -e "${GREEN}✅ 主配置文件已激活postgresql配置${NC}"
else
    echo -e "${RED}❌ 主配置文件未激活postgresql配置${NC}"
fi

# 检查PostgreSQL配置文件
if [ -f "src/main/resources/application-postgresql.yml" ]; then
    echo -e "${GREEN}✅ PostgreSQL配置文件存在${NC}"
else
    echo -e "${RED}❌ PostgreSQL配置文件不存在${NC}"
fi

# 检查数据源配置类
if [ -f "src/main/java/com/hujun/aicodehelper/config/MultiDataSourceConfig.java" ]; then
    echo -e "${GREEN}✅ 数据源配置类存在${NC}"
else
    echo -e "${RED}❌ 数据源配置类不存在${NC}"
fi

# 6. 检查Maven依赖
echo -e "\n${YELLOW}📦 检查Maven依赖...${NC}"

if grep -q "postgresql" pom.xml; then
    echo -e "${GREEN}✅ PostgreSQL驱动依赖存在${NC}"
else
    echo -e "${RED}❌ PostgreSQL驱动依赖缺失${NC}"
fi

# 7. 端口检查
echo -e "\n${YELLOW}🔌 检查端口占用...${NC}"

# 检查PostgreSQL端口
if nc -z 8.137.78.144 5433 2>/dev/null; then
    echo -e "${GREEN}✅ PostgreSQL端口5433可访问${NC}"
else
    echo -e "${RED}❌ PostgreSQL端口5433不可访问${NC}"
fi

echo -e "\n${YELLOW}=================================="
echo -e "🏁 数据库配置检查完成${NC}"

# 输出建议
echo -e "\n${YELLOW}💡 启动建议:${NC}"
echo "1. 确保PostgresML容器正在运行"
echo "2. 执行数据库初始化脚本"
echo "3. 使用 ./mvnw spring-boot:run 启动应用"
echo "4. 查看启动日志确认数据源初始化成功"

echo -e "\n${YELLOW}📚 文档参考:${NC}"
echo "- 详细配置: doc/SINGLE_DATABASE_SETUP_GUIDE.md"
echo "- PostgreSQL迁移: src/main/resources/db/POSTGRESQL_MIGRATION_GUIDE.md"