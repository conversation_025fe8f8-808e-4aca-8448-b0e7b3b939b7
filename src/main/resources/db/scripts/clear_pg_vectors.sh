#!/bin/bash

# PostgreSQL 向量数据清空脚本
# 用于切换embedding模型后清空旧的向量数据
# 使用场景：从 BAAI/bge-m3 切换到 dengcao/Qwen3-Embedding-0.6B:Q8_0

echo "🧹 开始清空PostgreSQL向量数据库..."
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 数据库连接参数
DB_HOST="localhost"
DB_PORT="5433"
DB_USER="postgresml"
DB_NAME="postgresml"
DB_PASSWORD=""

# 检查函数
check_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
        return 0
    else
        echo -e "${RED}❌ $2${NC}"
        return 1
    fi
}

# 1. 检查数据库连接
echo -e "\n${YELLOW}🔍 检查数据库连接...${NC}"
PGPASSWORD="$DB_PASSWORD" psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" 2>/dev/null >/dev/null
if ! check_status $? "数据库连接正常"; then
    echo -e "${RED}❌ 无法连接到PostgreSQL数据库，请检查连接参数${NC}"
    exit 1
fi

# 2. 显示当前向量数据统计
echo -e "\n${BLUE}📊 当前向量数据统计：${NC}"
PGPASSWORD="$DB_PASSWORD" psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT file_name) as unique_files,
    pg_size_pretty(pg_total_relation_size('embeddings')) as table_size
FROM embeddings;
"

# 3. 确认清空操作
echo -e "\n${YELLOW}⚠️  警告：此操作将删除所有向量数据！${NC}"
echo -e "${YELLOW}原因：切换embedding模型后，旧向量数据不兼容${NC}"
echo -e "${YELLOW}模型切换：BAAI/bge-m3 → dengcao/Qwen3-Embedding-0.6B:Q8_0${NC}"
echo ""
read -p "确认要清空所有向量数据吗？(输入 'YES' 确认): " confirm

if [ "$confirm" != "YES" ]; then
    echo -e "${YELLOW}❌ 操作已取消${NC}"
    exit 0
fi

# 4. 开始清空数据
echo -e "\n${YELLOW}🗑️  开始清空向量数据...${NC}"

# 删除所有向量数据
PGPASSWORD="$DB_PASSWORD" psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
-- 清空embeddings表
TRUNCATE TABLE embeddings RESTART IDENTITY CASCADE;
"
check_status $? "向量数据已清空"

# 5. 重建索引（可选，提高性能）
echo -e "\n${YELLOW}🔧 重建索引...${NC}"
PGPASSWORD="$DB_PASSWORD" psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
-- 重建向量索引
DROP INDEX IF EXISTS embeddings_embedding_idx;
CREATE INDEX embeddings_embedding_idx 
ON embeddings USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100);
"
check_status $? "索引重建完成"

# 6. 验证清空结果
echo -e "\n${BLUE}📊 清空后统计：${NC}"
PGPASSWORD="$DB_PASSWORD" psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
SELECT 
    COUNT(*) as total_records,
    pg_size_pretty(pg_total_relation_size('embeddings')) as table_size
FROM embeddings;
"

# 7. 显示完成信息
echo -e "\n${GREEN}🎉 向量数据库清空完成！${NC}"
echo -e "${GREEN}✅ 现在可以使用新的embedding模型重新生成向量${NC}"
echo -e "${BLUE}💡 提示：重启应用后，系统将使用 dengcao/Qwen3-Embedding-0.6B:Q8_0 模型${NC}"
echo ""
echo -e "${YELLOW}📝 后续步骤：${NC}"
echo "1. 确保Ollama服务正在运行: ollama serve"
echo "2. 确保已下载模型: ollama pull dengcao/Qwen3-Embedding-0.6B:Q8_0"
echo "3. 重启应用程序"
echo "4. 重新导入文档以生成新的向量数据"