#!/bin/bash

echo "🔄 正在更新PostgresML数据库结构..."
echo "📋 更新内容: 将向量维度从768维改为1024维"
echo "🤖 目标模型: BGE-large-en-v1.5"
echo ""

# 检查Docker容器是否运行
echo "🔍 检查PostgresML容器状态..."
CONTAINER_ID=$(docker ps -q --filter "ancestor=ghcr.io/postgresml/postgresml:2.9.3")

if [ -z "$CONTAINER_ID" ]; then
    echo "❌ PostgresML容器未运行，请先启动容器"
    exit 1
fi

echo "✅ 找到PostgresML容器: $CONTAINER_ID"
echo ""

# 复制SQL脚本到容器
echo "📋 复制SQL脚本到容器..."
docker cp src/main/resources/db/init_postgresml.sql $CONTAINER_ID:/tmp/

# 执行SQL脚本
echo "🗄️ 执行数据库更新脚本..."
docker exec -i $CONTAINER_ID psql -h localhost -U postgresml -d postgresml -f /tmp/init_postgresml.sql

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 数据库更新完成!"
    echo "📊 新配置:"
    echo "   - 向量维度: 1024"
    echo "   - 支持模型: BGE-large-en-v1.5"
    echo "   - 表已重建: embeddings"
    echo ""
    echo "🚀 现在可以启动SpringBoot应用了:"
    echo "   ./mvnw spring-boot:run"
else
    echo ""
    echo "❌ 数据库更新失败，请检查错误信息"
    exit 1
fi 