-- PostgreSQL 权限修复脚本
-- 需要用 postgres 超级用户或数据库所有者执行

-- 1. 连接到 postgresml 数据库
\c postgresml;

-- 2. 授予 myappuser 用户在 public schema 上的权限
GRANT ALL PRIVILEGES ON SCHEMA public TO myappuser;
GRANT CREATE ON SCHEMA public TO myappuser;
GRANT USAGE ON SCHEMA public TO myappuser;

-- 3. 授予对现有表的权限（如果有的话）
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO myappuser;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO myappuser;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO myappuser;

-- 4. 设置默认权限（对未来创建的对象）
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO myappuser;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO myappuser;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO myappuser;

-- 5. 确保用户可以创建扩展（pgvector需要）
ALTER USER myappuser CREATEDB;

-- 6. 验证权限
\dp public.*;
SELECT schema_name, schema_owner FROM information_schema.schemata WHERE schema_name = 'public';