-- PostgreSQL 向量数据清空SQL脚本
-- 用于切换embedding模型后清空旧的向量数据
-- 使用场景：从 BAAI/bge-m3 切换到 dengcao/Qwen3-Embedding-0.6B:Q8_0

-- 显示清空前的统计信息
SELECT 
    '=== 清空前统计 ===' as info,
    COUNT(*) as total_records,
    COUNT(DISTINCT file_name) as unique_files,
    pg_size_pretty(pg_total_relation_size('embeddings')) as table_size
FROM embeddings;

-- 清空embeddings表（保留表结构）
TRUNCATE TABLE embeddings RESTART IDENTITY CASCADE;

-- 重建向量索引以优化性能
DROP INDEX IF EXISTS embeddings_embedding_idx;
CREATE INDEX embeddings_embedding_idx 
ON embeddings USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100);

-- 显示清空后的统计信息
SELECT 
    '=== 清空后统计 ===' as info,
    COUNT(*) as total_records,
    pg_size_pretty(pg_total_relation_size('embeddings')) as table_size
FROM embeddings;

-- 显示表结构确认
\d embeddings;

-- 完成提示
SELECT '🎉 向量数据库清空完成！现在可以使用新的embedding模型重新生成向量' as status;