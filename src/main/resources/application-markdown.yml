spring:
  application:
    name: ai-code-helper-markdown
  # 禁用数据库相关配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
      - org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration

server:
  port: 8081
  servlet:
    context-path: /api

# Ollama本地模型配置
langchain4j:
  ollama:
    chat-model:
      base-url: http://localhost:11434
      model-name: gemma3n:e4b
      temperature: 0.7
      max-tokens: 16384

# 日志配置
logging:
  level:
    com.hujun.aicodehelper.service.TxtToMarkdownService: INFO
    com.hujun.aicodehelper.controller.MarkdownController: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"