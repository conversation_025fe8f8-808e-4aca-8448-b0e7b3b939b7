package com.hujun.aicodehelper.chat.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 聊天记忆配置属性类
 */
@Data
@Component
@ConfigurationProperties(prefix = "chat.memory")
public class ChatMemoryProperties {

    /**
     * 聊天记忆类型：message（消息窗口） 或 token（令牌窗口）
     */
    private String type = "message";

    /**
     * 最大消息数量（仅用于消息窗口模式）
     */
    private int maxMessages = 20;

    /**
     * 最大令牌数量（仅用于令牌窗口模式）
     */
    private int maxTokens = 1000;
}