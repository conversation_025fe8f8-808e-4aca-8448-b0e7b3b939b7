package com.hujun.aicodehelper.chat.service;

import com.hujun.aicodehelper.chat.repository.ChatMessageRepository;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.memory.chat.ChatMemoryProvider;
import dev.langchain4j.memory.ChatMemory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 聊天记忆服务
 * 提供便捷的聊天记忆管理功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatMemoryService {

    private final ChatMemoryProvider chatMemoryProvider;
    
    @Qualifier("userChatMemoryProvider")
    private final ChatMemoryProvider userChatMemoryProvider;
    
    private final ChatMessageRepository chatMessageRepository;

    /**
     * 获取指定会话的聊天记忆
     * 
     * @param sessionId 会话ID
     * @return ChatMemory
     */
    public ChatMemory getChatMemory(String sessionId) {
        log.debug("🔍 获取聊天记忆，会话ID: {}", sessionId);
        return chatMemoryProvider.get(sessionId);
    }

    /**
     * 获取指定用户的聊天记忆
     * 
     * @param userId 用户ID
     * @return ChatMemory
     */
    public ChatMemory getUserChatMemory(String userId) {
        log.debug("👤 获取用户聊天记忆，用户ID: {}", userId);
        return userChatMemoryProvider.get(userId);
    }

    /**
     * 向指定会话添加消息
     * 
     * @param sessionId 会话ID
     * @param message 聊天消息
     */
    public void addMessage(String sessionId, ChatMessage message) {
        log.debug("➕ 添加消息到会话，会话ID: {}, 消息类型: {}", sessionId, message.type());
        ChatMemory memory = getChatMemory(sessionId);
        memory.add(message);
    }

    /**
     * 向指定用户会话添加消息
     * 
     * @param userId 用户ID
     * @param message 聊天消息
     */
    public void addUserMessage(String userId, ChatMessage message) {
        log.debug("👤 添加消息到用户会话，用户ID: {}, 消息类型: {}", userId, message.type());
        ChatMemory memory = getUserChatMemory(userId);
        memory.add(message);
    }

    /**
     * 获取指定会话的所有消息
     * 
     * @param sessionId 会话ID
     * @return 消息列表
     */
    public List<ChatMessage> getMessages(String sessionId) {
        log.debug("📜 获取会话消息，会话ID: {}", sessionId);
        ChatMemory memory = getChatMemory(sessionId);
        return memory.messages();
    }

    /**
     * 获取指定用户的所有消息
     * 
     * @param userId 用户ID
     * @return 消息列表
     */
    public List<ChatMessage> getUserMessages(String userId) {
        log.debug("👤 获取用户消息，用户ID: {}", userId);
        ChatMemory memory = getUserChatMemory(userId);
        return memory.messages();
    }

    /**
     * 清空指定会话的聊天记忆
     * 
     * @param sessionId 会话ID
     */
    public void clearChatMemory(String sessionId) {
        log.info("🗑️ 清空会话聊天记忆，会话ID: {}", sessionId);
        ChatMemory memory = getChatMemory(sessionId);
        memory.clear();
    }

    /**
     * 清空指定用户的聊天记忆
     * 
     * @param userId 用户ID
     */
    public void clearUserChatMemory(String userId) {
        log.info("👤 清空用户聊天记忆，用户ID: {}", userId);
        ChatMemory memory = getUserChatMemory(userId);
        memory.clear();
    }

    /**
     * 检查指定会话是否有聊天记录
     * 
     * @param sessionId 会话ID
     * @return 是否存在记录
     */
    public boolean hasMessages(String sessionId) {
        return chatMessageRepository.existsByMemoryId(sessionId);
    }

    /**
     * 检查指定用户是否有聊天记录
     * 
     * @param userId 用户ID
     * @return 是否存在记录
     */
    public boolean hasUserMessages(String userId) {
        String memoryId = "user_" + userId;
        return chatMessageRepository.existsByMemoryId(memoryId);
    }

    /**
     * 获取指定会话的消息数量
     * 
     * @param sessionId 会话ID
     * @return 消息数量
     */
    public long getMessageCount(String sessionId) {
        return chatMessageRepository.countByMemoryId(sessionId);
    }

    /**
     * 获取指定用户的消息数量
     * 
     * @param userId 用户ID
     * @return 消息数量
     */
    public long getUserMessageCount(String userId) {
        String memoryId = "user_" + userId;
        return chatMessageRepository.countByMemoryId(memoryId);
    }
}