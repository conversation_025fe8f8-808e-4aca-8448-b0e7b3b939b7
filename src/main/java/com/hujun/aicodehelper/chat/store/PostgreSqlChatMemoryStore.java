package com.hujun.aicodehelper.chat.store;

import com.hujun.aicodehelper.chat.entity.ChatMessageEntity;
import com.hujun.aicodehelper.chat.repository.ChatMessageRepository;
import dev.langchain4j.store.memory.chat.ChatMemoryStore; // 将 ChatMemoryStore 移动到 dev.langchain4j.store.memory.chat 包下
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.ChatMessageDeserializer;
import dev.langchain4j.data.message.ChatMessageSerializer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.IntStream;

/**
 * PostgreSQL 聊天记忆存储实现
 * 实现 LangChain4j ChatMemoryStore 接口，将聊天记录持久化到 PostgreSQL 数据库
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PostgreSqlChatMemoryStore implements ChatMemoryStore {

    private final ChatMessageRepository chatMessageRepository;

    /**
     * 根据内存ID获取所有消息
     * 
     * @param memoryId 内存ID，用于区分不同的聊天会话
     * @return 聊天消息列表
     */
    @Override
    public List<ChatMessage> getMessages(Object memoryId) {
        String memoryIdStr = memoryId.toString();
        log.info("🔍 获取聊天记录，内存ID: {}", memoryIdStr);
        
        try {
            List<ChatMessageEntity> entities = chatMessageRepository.findByMemoryIdOrderByMessageOrder(memoryIdStr);
            List<ChatMessage> messages = entities.stream()
                    .map(entity -> ChatMessageDeserializer.messageFromJson(entity.getMessageContent()))
                    .toList();
            
            log.info("✅ 成功获取 {} 条聊天记录，内存ID: {}", messages.size(), memoryIdStr);
            
            // 详细记录每条消息的类型和顺序
            for (int i = 0; i < messages.size(); i++) {
                ChatMessage message = messages.get(i);
                String messageText = getMessageText(message);
                log.info("📝 消息[{}]: 类型={}, 内容长度={}", 
                    i, message.getClass().getSimpleName(), 
                    messageText != null ? messageText.length() : 0);
                    
                // 特别关注工具相关消息
                if (message instanceof dev.langchain4j.data.message.AiMessage) {
                    var aiMsg = (dev.langchain4j.data.message.AiMessage) message;
                    if (aiMsg.hasToolExecutionRequests()) {
                        log.info("🔧 [存储]AI消息包含工具调用请求: 数量={}", 
                            aiMsg.toolExecutionRequests().size());
                    }
                } else if (message instanceof dev.langchain4j.data.message.ToolExecutionResultMessage) {
                    var toolMsg = (dev.langchain4j.data.message.ToolExecutionResultMessage) message;
                    log.info("🛠️ [存储]工具执行结果消息: 工具名={}, 结果长度={}", 
                        toolMsg.toolName(), toolMsg.text() != null ? toolMsg.text().length() : 0);
                }
            }
            
            return messages;
        } catch (Exception e) {
            log.error("❌ 获取聊天记录失败，内存ID: {}, 错误: {}", memoryIdStr, e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * 更新内存中的所有消息
     * 此方法会完全替换指定内存ID的所有消息
     * 
     * @param memoryId 内存ID
     * @param messages 新的消息列表
     */
    @Override
    @Transactional
    public void updateMessages(Object memoryId, List<ChatMessage> messages) {
        String memoryIdStr = memoryId.toString();
        log.info("🔄 更新聊天记录，内存ID: {}, 消息数量: {}", memoryIdStr, messages.size());
        
        // 详细记录即将保存的消息
        for (int i = 0; i < messages.size(); i++) {
            ChatMessage message = messages.get(i);
            String messageText = getMessageText(message);
            log.info("💾 准备保存消息[{}]: 类型={}, 内容长度={}", 
                i, message.getClass().getSimpleName(), 
                messageText != null ? messageText.length() : 0);
                
            // 特别关注工具相关消息
            if (message instanceof dev.langchain4j.data.message.AiMessage) {
                var aiMsg = (dev.langchain4j.data.message.AiMessage) message;
                if (aiMsg.hasToolExecutionRequests()) {
                    log.info("🔧 [即将保存]AI消息包含工具调用请求: 数量={}", 
                        aiMsg.toolExecutionRequests().size());
                }
            } else if (message instanceof dev.langchain4j.data.message.ToolExecutionResultMessage) {
                var toolMsg = (dev.langchain4j.data.message.ToolExecutionResultMessage) message;
                log.info("🛠️ [即将保存]工具执行结果消息: 工具名={}, 结果长度={}", 
                    toolMsg.toolName(), toolMsg.text() != null ? toolMsg.text().length() : 0);
            }
        }
        
        try {
            // 首先删除该内存ID的所有现有消息
            long existingCount = chatMessageRepository.countByMemoryId(memoryIdStr);
            log.info("🗑️ 删除现有消息，内存ID: {}, 现有消息数量: {}", memoryIdStr, existingCount);
            chatMessageRepository.deleteByMemoryId(memoryIdStr);
            
            // 然后保存新的消息列表
            List<ChatMessageEntity> entities = IntStream.range(0, messages.size())
                    .mapToObj(i -> {
                        ChatMessage message = messages.get(i);
                        ChatMessageEntity entity = new ChatMessageEntity();
                        entity.setMemoryId(memoryIdStr);
                        entity.setMessageOrder(i);
                        entity.setMessageType(message.type().name());
                        entity.setMessageContent(ChatMessageSerializer.messageToJson(message));
                        return entity;
                    })
                    .toList();
            
            chatMessageRepository.saveAll(entities);
            log.info("✅ 成功更新聊天记录，内存ID: {}, 保存消息数量: {}", memoryIdStr, entities.size());
        } catch (Exception e) {
            log.error("❌ 更新聊天记录失败，内存ID: {}, 错误: {}", memoryIdStr, e.getMessage(), e);
            throw new RuntimeException("更新聊天记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除指定内存ID的所有消息
     * 
     * @param memoryId 内存ID
     */
    @Override
    @Transactional
    public void deleteMessages(Object memoryId) {
        String memoryIdStr = memoryId.toString();
        log.info("🗑️ 删除聊天记录，内存ID: {}", memoryIdStr);
        
        try {
            long deletedCount = chatMessageRepository.countByMemoryId(memoryIdStr);
            chatMessageRepository.deleteByMemoryId(memoryIdStr);
            log.info("✅ 成功删除聊天记录，内存ID: {}, 删除数量: {}", memoryIdStr, deletedCount);
        } catch (Exception e) {
            log.error("❌ 删除聊天记录失败，内存ID: {}, 错误: {}", memoryIdStr, e.getMessage(), e);
            throw new RuntimeException("删除聊天记录失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 辅助方法：获取消息文本内容
     */
    private String getMessageText(ChatMessage message) {
        if (message instanceof dev.langchain4j.data.message.UserMessage) {
            return ((dev.langchain4j.data.message.UserMessage) message).singleText();
        } else if (message instanceof dev.langchain4j.data.message.AiMessage) {
            return ((dev.langchain4j.data.message.AiMessage) message).text();
        } else if (message instanceof dev.langchain4j.data.message.SystemMessage) {
            return ((dev.langchain4j.data.message.SystemMessage) message).text();
        } else if (message instanceof dev.langchain4j.data.message.ToolExecutionResultMessage) {
            return ((dev.langchain4j.data.message.ToolExecutionResultMessage) message).text();
        }
        return null;
    }
}