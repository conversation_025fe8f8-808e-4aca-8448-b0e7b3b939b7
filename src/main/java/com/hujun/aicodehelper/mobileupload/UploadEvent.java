package com.hujun.aicodehelper.mobileupload;

import java.time.Instant;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UploadEvent {
    private String sessionId;
    private String type;        // waiting | scanned | uploading | uploaded | expired | cancelled
    private String imageUrl;    // optional
    private String message;     // optional
    private Instant timestamp = Instant.now();

    public static UploadEvent of(String sessionId, String type) {
        return new UploadEvent(sessionId, type, null, null, Instant.now());
    }

    public static UploadEvent uploaded(String sessionId, String imageUrl) {
        return new UploadEvent(sessionId, "uploaded", imageUrl, null, Instant.now());
    }
}

