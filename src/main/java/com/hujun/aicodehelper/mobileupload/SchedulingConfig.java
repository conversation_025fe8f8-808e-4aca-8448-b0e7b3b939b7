package com.hujun.aicodehelper.mobileupload;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
@EnableScheduling
public class SchedulingConfig {

    @Resource
    private UploadSessionService service;

    // Run every 60 seconds
    @Scheduled(fixedRate = 60000, initialDelay = 60000)
    public void cleanupExpiredSessions() {
        try {
            service.expireOldSessions();
        } catch (Exception e) {
            log.warn("Error while expiring upload sessions: {}", e.getMessage());
        }
    }
}

