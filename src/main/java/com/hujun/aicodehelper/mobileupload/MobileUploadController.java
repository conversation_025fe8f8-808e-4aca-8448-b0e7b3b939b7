package com.hujun.aicodehelper.mobileupload;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import org.springframework.http.codec.ServerSentEvent;

@Slf4j
@RestController
@RequestMapping("/mobile-upload")
public class MobileUploadController {

    @Resource
    private UploadSessionService service;

    @Data
    public static class StartRequest {
        private String memoryId;
    }

    @PostMapping("/session/start")
    public Map<String, Object> start(@RequestBody StartRequest req) {
        String memoryId = StringUtils.hasText(req.getMemoryId()) ? req.getMemoryId() : String.valueOf(System.currentTimeMillis());
        UploadSessionService.CreateSessionResult res = service.createSession(memoryId);
        Map<String, Object> map = new HashMap<>();
        map.put("sessionId", res.sessionId);
        map.put("token", res.token);
        map.put("expiresAt", res.expiresAt.toString());
        return map;
    }

    @GetMapping("/session/stream")
    public Flux<ServerSentEvent<Map<String, Object>>> stream(@RequestParam String sessionId) {
        return service.stream(sessionId)
            .map(ev -> {
                Map<String, Object> data = new HashMap<>();
                data.put("sessionId", ev.getSessionId());
                data.put("type", ev.getType());
                if (ev.getImageUrl() != null) data.put("imageUrl", ev.getImageUrl());
                data.put("timestamp", Instant.now().toString());
                return ServerSentEvent.<Map<String, Object>>builder().data(data).build();
            });
    }

    @GetMapping("/session/status")
    public Map<String, Object> status(@RequestParam String sessionId) {
        Map<String, Object> data = new HashMap<>();
        service.getBySessionId(sessionId).ifPresentOrElse(s -> {
            data.put("sessionId", s.getSessionId());
            data.put("status", s.getStatus().name().toLowerCase());
            data.put("imageUrl", s.getImageUrl());
            data.put("expiresAt", s.getExpiresAt().toString());
        }, () -> {
            data.put("error", "not_found");
        });
        return data;
    }

    @PostMapping("/mark-scanned")
    public Map<String, Object> markScanned(@RequestParam String token) {
        service.markScanned(token);
        Map<String, Object> res = new HashMap<>();
        res.put("success", true);
        return res;
    }

    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Map<String, Object> upload(@RequestParam String token, @RequestParam("file") MultipartFile file) throws IOException {
        String url = service.handleUpload(token, file);
        Map<String, Object> res = new HashMap<>();
        res.put("imageUrl", url);
        return res;
    }

    @GetMapping("/file/{yyyy}/{mm}/{dd}/{filename}")
    public ResponseEntity<byte[]> getFile(
        @PathVariable String yyyy,
        @PathVariable String mm,
        @PathVariable String dd,
        @PathVariable String filename
    ) throws IOException {
        Path p = Paths.get("data/uploads", yyyy, mm, dd, filename);
        if (!p.toFile().exists()) return ResponseEntity.notFound().build();
        MediaType mt = service.resolveMediaType(p);
        byte[] bytes = java.nio.file.Files.readAllBytes(p);
        return ResponseEntity.ok()
            .header(HttpHeaders.CACHE_CONTROL, "public, max-age=31536000, immutable")
            .contentType(mt)
            .body(bytes);
    }
}

