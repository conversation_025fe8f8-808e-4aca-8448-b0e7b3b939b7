package com.hujun.aicodehelper.ai.enhanced;

import com.hujun.aicodehelper.ai.RagService;
import com.hujun.aicodehelper.ai.model.PostgresVectorStore;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 增强版RAG服务，支持年级和主题过滤
 */
@Slf4j
@Service
public class EnhancedRagService extends RagService {

    @Resource
    private EmbeddingModel embeddingModel;

    @Resource
    private PostgresVectorStore postgresVectorStore;

    /**
     * 带过滤条件的知识库搜索
     * @param query 搜索查询
     * @param maxResults 最大结果数
     * @param minScore 最小相似度分数
     * @param grade 年级过滤器（可选）
     * @param topic 主题过滤器（可选）
     * @return 匹配的文档片段
     */
    public List<RagSearchResult> searchKnowledgeWithFilters(String query, int maxResults, double minScore, 
                                                           String grade, String topic) {
        log.info("🔍 开始增强搜索知识库 - 查询: '{}', 最大结果: {}, 最小分数: {}, 年级: {}, 主题: {}", 
                query, maxResults, minScore, grade, topic);

        // 向量化查询
        Embedding queryEmbedding = embeddingModel.embed(query).content();

        // 构建元数据过滤器
        Map<String, Object> metadataFilters = new HashMap<>();
        if (grade != null && !grade.isEmpty()) {
            metadataFilters.put("grade", grade);
        }
        if (topic != null && !topic.isEmpty()) {
            metadataFilters.put("topic", topic);
        }

        // 执行带过滤的搜索
        List<EmbeddingMatch<TextSegment>> matches = postgresVectorStore.findRelevant(
                queryEmbedding, maxResults, minScore, metadataFilters);

        // 转换为结果对象
        List<RagSearchResult> results = new ArrayList<>();
        for (EmbeddingMatch<TextSegment> match : matches) {
            results.add(new RagSearchResult(
                    match.embedded().text(),
                    match.score(),
                    match.embedded().metadata().getString("file_name"),
                    match.embedded().metadata().toMap()
            ));
        }

        log.info("🎯 增强搜索完成，返回 {} 个结果", results.size());
        return results;
    }

    /**
     * 智能年级映射
     * @param gradeText 年级文本描述
     * @return 标准化年级代码
     */
    public String mapGrade(String gradeText) {
        if (gradeText == null || gradeText.isEmpty()) {
            return null;
        }

        // 统一转换为小写进行匹配
        String lowerGrade = gradeText.toLowerCase();

        // 小学年级映射
        if (lowerGrade.contains("小学") || lowerGrade.contains("年级")) {
            if (lowerGrade.contains("一") || lowerGrade.contains("1")) return "P1";
            if (lowerGrade.contains("二") || lowerGrade.contains("2")) return "P2";
            if (lowerGrade.contains("三") || lowerGrade.contains("3")) return "P3";
            if (lowerGrade.contains("四") || lowerGrade.contains("4")) return "P4";
            if (lowerGrade.contains("五") || lowerGrade.contains("5")) return "P5";
            if (lowerGrade.contains("六") || lowerGrade.contains("6")) return "P6";
        }

        // 中学年级映射
        if (lowerGrade.contains("初中") || lowerGrade.contains("初") || lowerGrade.contains("七") || lowerGrade.contains("7")) {
            return " secondary";
        }

        // 高中年级映射
        if (lowerGrade.contains("高中") || lowerGrade.contains("高")) {
            return "high";
        }

        return null; // 无法映射
    }
}