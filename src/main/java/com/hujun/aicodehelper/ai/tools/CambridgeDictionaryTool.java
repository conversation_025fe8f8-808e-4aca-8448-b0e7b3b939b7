package com.hujun.aicodehelper.ai.tools;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.context.annotation.Configuration;

import dev.langchain4j.agent.tool.P;
import dev.langchain4j.agent.tool.Tool;
import lombok.extern.slf4j.Slf4j;

/**
 * 剑桥词典查询工具
 */
@Configuration
@Slf4j
public class CambridgeDictionaryTool {

    /**
     * 从剑桥词典网站查询单词定义、音标、例句等信息
     *
     * @param word 要查询的单词
     * @return 词典查询结果，包含定义、音标、例句等，若失败则返回错误信息
     */

    /**
     * 直接查询模式 - 完全绕过AI处理，返回原始数据
     * 用于需要保持完整格式的场景
     */
    @Tool(name = "dictionarySearchRaw", value = """
            DIRECT MODE: Searches Cambridge Dictionary and returns RAW, unprocessed data.
            This tool bypasses AI interpretation completely to preserve exact formatting.
            Use when you need the original dictionary content without any modifications.
            """)
    public String searchWordDirect(@P(value = "the English word to search") String word) {
        String result = searchWordInternal(word);
        if (result.startsWith("查询失败") || result.startsWith("未找到单词")) {
            return result;
        }

        // 添加直接模式标识，告诉前端这是原始数据
        return "🔒 DIRECT_MODE_RESULT\n" + result;
    }

    /**
     * 内部查询方法，提取公共逻辑
     */
    @Tool(name = "dictionarySearch", value = """
            Searches Cambridge Dictionary for word definitions, pronunciations, and examples.
            Use this tool when the user wants to look up English words, get definitions,
            pronunciations, or example sentences. The input should be a single English word.

            CRITICAL: This tool returns RAW dictionary data. DO NOT process, rephrase, or modify
            the content in any way. Present the results EXACTLY as returned by the tool,
            including all pronunciation symbols, formatting, and special characters.
            """)
    public String searchWord(@P(value = "the English word to search") String word) {
        return searchWordInternal(word);
    }

    /**
     * 内部查询方法，提取公共逻辑
     */
    private String searchWordInternal(String word) {
        log.info("开始查询单词: {}", word);
        StringBuilder result = new StringBuilder();

        // 构建搜索URL
        String encodedWord = URLEncoder.encode(word.trim().toLowerCase(), StandardCharsets.UTF_8);
        String url = "https://dictionary.cambridge.org/dictionary/english/" + encodedWord;

        // 发送请求并解析页面
        Document doc;
        try {
            doc = Jsoup.connect(url)
                    .userAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                    .timeout(10000)
                    .get();
        } catch (IOException e) {
            log.error("Failed to fetch dictionary page for word: {}", word, e);
            return "查询失败: " + e.getMessage();
        }

        // 检查是否找到单词
        Elements wordNotFound = doc.select(".no-result");
        if (!wordNotFound.isEmpty()) {
            return "未找到单词 '" + word + "' 的定义";
        }

        result.append("=== ").append(word.toUpperCase()).append(" ===\n")
                .append("📚 剑桥词典查询结果 📚\n\n");

        // 提取单词头部信息（包含音标）
        Elements headwordElements = doc.select(".headword");
        if (!headwordElements.isEmpty()) {
            String headword = headwordElements.first().text();
            result.append("单词: ").append(headword).append("\n");
        }

        // 提取音标 - 使用特殊标记保护格式
        Elements pronunciationElements = doc.select(".pron .ipa");
        if (!pronunciationElements.isEmpty()) {
            result.append("音标: 【PRONUNCIATION_START】");
            for (Element pronElement : pronunciationElements) {
                result.append(pronElement.text()).append(" ");
            }
            result.append("【PRONUNCIATION_END】\n");
        }

        // 提取词性和定义
        Elements posBlocks = doc.select(".pos-body");
        for (Element posBlock : posBlocks) {
            // 获取词性
            Elements posElements = posBlock.select(".pos");
            if (!posElements.isEmpty()) {
                result.append("\n词性: ").append(posElements.first().text()).append("\n");
            }

            // 获取定义和例句
            Elements defBlocks = posBlock.select(".def-block");
            for (int i = 0; i < defBlocks.size() && i < 3; i++) { // 限制显示前3个定义
                Element defBlock = defBlocks.get(i);

                // 定义
                Elements defElements = defBlock.select(".def");
                if (!defElements.isEmpty()) {
                    result.append("定义").append(i + 1).append(": ").append(defElements.first().text()).append("\n");
                }

                // 例句 - 保护特殊格式
                Elements exampleElements = defBlock.select(".examp .eg");
                if (!exampleElements.isEmpty()) {
                    result.append("例句: 【EXAMPLE_START】").append(exampleElements.first().text())
                            .append("【EXAMPLE_END】\n");
                }
            }
        }

        // 提取短语（如果有）
        Elements phraseBlocks = doc.select(".phrase-block");
        if (!phraseBlocks.isEmpty()) {
            result.append("\n=== 相关短语 ===\n");
            for (int i = 0; i < phraseBlocks.size() && i < 3; i++) { // 限制显示前3个短语
                Element phraseBlock = phraseBlocks.get(i);
                Elements phraseElements = phraseBlock.select(".phrase");
                Elements phraseDefElements = phraseBlock.select(".def");

                if (!phraseElements.isEmpty() && !phraseDefElements.isEmpty()) {
                    result.append("短语: ").append(phraseElements.first().text())
                            .append(" - ").append(phraseDefElements.first().text()).append("\n");
                }
            }
        }

        String finalResult = result.toString().trim();
        if (finalResult.equals("=== " + word.toUpperCase() + " ===")) {
            return "未能解析到 '" + word + "' 的详细信息，请检查单词拼写或稍后重试";
        }

        // 确保返回内容不为空且格式正确
        if (finalResult.isEmpty()) {
            return "词典查询返回空结果，请稍后重试";
        }

        // 清理可能导致AI解析问题的特殊字符
        finalResult = finalResult.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", "");

        log.info("查询单词 '{}' 完成，返回内容长度: {}", word, finalResult.length());

        return finalResult;
    }
}