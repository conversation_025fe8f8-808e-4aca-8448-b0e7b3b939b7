package com.hujun.aicodehelper.ai.model;

import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.StreamingChatModel;
import dev.langchain4j.model.chat.listener.ChatModelListener;
import dev.langchain4j.model.googleai.GoogleAiGeminiChatModel;
import dev.langchain4j.model.googleai.GoogleAiGeminiStreamingChatModel;
import jakarta.annotation.Resource;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;

import java.util.List;

// 注释掉此配置类，现在使用 MultiGeminiModelConfig 替代
// @Configuration
@ConfigurationProperties(prefix = "langchain4j.google.ai.gemini.chat-model")
@Data
public class GeminiChatModelConfig {

    private String apiKey;
    private String modelName;

    @Resource
    private ChatModelListener chatModelListener;

    // 这些Bean现在由 MultiGeminiModelConfig 提供
    // @Bean
    public ChatModel myGeminiChatModel() {
        return GoogleAiGeminiChatModel.builder()
                .apiKey(apiKey)
                .modelName(modelName)
                .listeners(List.of(chatModelListener))
                .build();
    }

    // @Bean
    public StreamingChatModel myGeminiStreamingChatModel() {
        return GoogleAiGeminiStreamingChatModel.builder()
                .apiKey(apiKey)
                .modelName(modelName)
                .listeners(List.of(chatModelListener))
                .build();
    }
}
