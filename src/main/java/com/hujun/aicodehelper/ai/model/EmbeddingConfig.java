package com.hujun.aicodehelper.ai.model;

import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
//import dev.langchain4j.model.googleai.GoogleAiEmbeddingModel;

import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.inmemory.InMemoryEmbeddingStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Qualifier;

@Slf4j
@Configuration
public class EmbeddingConfig {

    @Value("${embedding.model-type:local}")
    private String modelType;

    // 本地模型配置
    @Value("${embedding.local.base-url:http://localhost:9000}")
    private String localBaseUrl;

    @Value("${embedding.local.model-name:bge-large-en-v1.5}")
    private String localModelName;

    @Value("${embedding.local.dimension:1024}")
    private int localDimension;

    // Gemini模型配置
    @Value("${embedding.gemini.api-key:}")
    private String geminiApiKey;

    @Value("${embedding.gemini.model-name:gemini-embedding-001}")
    private String geminiModelName;

    @Value("${embedding.gemini.dimension:768}")
    private int geminiDimension;

    @Bean
    public EmbeddingModel embeddingModel() {
        log.info("🤖 正在初始化嵌入模型...");
        log.info("📋 模型类型: {}", modelType);

        switch (modelType.toLowerCase()) {
            case "local":
            default:
                return createLocalEmbeddingModel();
            case "gemini":
                return createGeminiEmbeddingModel();
        }
    }

    /**
     * 创建本地嵌入模型
     */
    private EmbeddingModel createLocalEmbeddingModel() {
        log.info("🏠 正在创建本地嵌入模型...");
        log.info("📋 本地模型配置信息:");
        log.info("   - 服务地址: {}", localBaseUrl);
        log.info("   - 模型名称: {}", localModelName);
        log.info("   - 向量维度: {}", localDimension);

        LocalEmbeddingModel model = new LocalEmbeddingModel(localBaseUrl, localModelName, localDimension);
        
        // 测试连接
        if (model.testConnection()) {
            log.info("✅ 本地嵌入模型初始化完成");
        } else {
            log.warn("⚠️ 本地嵌入模型连接测试失败，但仍继续初始化");
        }
        
        return model;
    }

    /**
     * 创建Gemini嵌入模型
     */
    private EmbeddingModel createGeminiEmbeddingModel() {
        log.info("🌟 正在创建自定义Gemini嵌入模型...");
        log.info("📋 Gemini模型配置信息:");
        log.info("   - 模型名称: {}", geminiModelName);
        log.info("   - 向量维度: {}", geminiDimension);
        log.info("   - API密钥: {}***", StringUtils.hasText(geminiApiKey) ? geminiApiKey.substring(0, Math.min(10, geminiApiKey.length())) : "未配置");

        if (!StringUtils.hasText(geminiApiKey)) {
            log.error("❌ Gemini API密钥未配置，请在配置文件中设置 embedding.gemini.api-key");
            throw new IllegalArgumentException("Gemini API密钥未配置");
        }

        try {
            // 使用自定义的Google AI嵌入模型，支持outputDimensionality参数
            EmbeddingModel model = new CustomGoogleAiEmbeddingModel(
                    geminiApiKey,
                    geminiModelName,
                    geminiDimension
            );
            
            log.info("✅ 自定义Gemini嵌入模型初始化完成，支持{}维向量输出", geminiDimension);
            return model;
        } catch (Exception e) {
            log.error("❌ Gemini嵌入模型初始化失败: {}", e.getMessage());
            throw new RuntimeException("Gemini嵌入模型初始化失败", e);
        }
    }



    @Bean
    public EmbeddingStore<TextSegment> embeddingStore(@Qualifier("postgresqlDataSource") DataSource postgresqlDataSource, EmbeddingModel embeddingModel) {
        log.info("💾 正在初始化向量数据库...");
        
        // 获取当前模型的向量维度
        int vectorDimension = getCurrentModelDimension();
        
        log.info("📋 数据库配置信息:");
        log.info("   - 数据库类型: PostgreSQL + pgvector");
        log.info("   - 向量维度: {} ({})", vectorDimension, modelType);
        log.info("   - 相似度算法: 余弦相似度");
        
        try {
            EmbeddingStore<TextSegment> store = new PostgresVectorStore(postgresqlDataSource, embeddingModel, vectorDimension);
            log.info("✅ PostgreSQL 向量数据库初始化完成");
            log.info("🎯 特性支持:");
            log.info("   - 持久化存储: ✅");
            log.info("   - 向量索引: IVFFlat");
            log.info("   - 元数据存储: JSONB");
            log.info("   - 文件名索引: ✅");
            return store;
        } catch (Exception e) {
            log.error("❌ PostgreSQL 向量数据库初始化失败: {}", e.getMessage());
            log.warn("🔄 回退到内存向量数据库...");
            EmbeddingStore<TextSegment> store = new InMemoryEmbeddingStore<>();
            log.info("✅ 内存向量数据库初始化完成 (回退模式)");
            return store;
        }
    }

    /**
     * 获取当前模型的向量维度
     */
    private int getCurrentModelDimension() {
        return switch (modelType.toLowerCase()) {
            case "local" -> localDimension;
            case "gemini" -> geminiDimension;
            default -> localDimension;
        };
    }

    /**
     * 获取当前模型类型
     */
    public String getModelType() {
        return modelType;
    }

    /**
     * 获取当前模型的向量维度 (公共方法)
     */
    public int getVectorDimension() {
        return getCurrentModelDimension();
    }
} 