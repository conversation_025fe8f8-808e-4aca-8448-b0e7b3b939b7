package com.hujun.aicodehelper.ai.model;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.sql.DataSource;

import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.model.embedding.EmbeddingModel;
import lombok.extern.slf4j.Slf4j;

/**
 * 专门用于操作 math_rag_knowledge 表的向量存储实现
 * 处理数学知识库的结构化数据存储和检索
 */
@Slf4j
public class MathRagKnowledgeStore {

    private final DataSource dataSource;
    private final EmbeddingModel embeddingModel;

    public MathRagKnowledgeStore(DataSource dataSource, EmbeddingModel embeddingModel) {
        this.dataSource = dataSource;
        this.embeddingModel = embeddingModel;
    }

    /**
     * 添加数学知识条目到 math_rag_knowledge 表
     */
    public String addMathKnowledge(String id, String type, String content, String topic, String grade,
                                   String answer, String latex, String sympyExpr, String graph,
                                   String difficulty, String source, String imagesJson, String stepsJson,
                                   String metadataJson) {
        log.debug("📝 正在添加数学知识到数据库: {}", id);

        String insertSQL = """
                INSERT INTO math_rag_knowledge (
                    id, type, content, answer, latex, sympy_expr, graph, topic, grade,
                    difficulty, source, images, steps, metadata, embedding
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?::jsonb, ?::jsonb, ?::jsonb, ?::vector(1536))
                """;

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(insertSQL)) {

            // 嵌入内容以生成向量
            Embedding embedding = embeddingModel.embed(content).content();

            stmt.setString(1, id);
            stmt.setString(2, type);
            stmt.setString(3, content);
            stmt.setString(4, answer);
            stmt.setString(5, latex);
            stmt.setString(6, sympyExpr);
            stmt.setString(7, graph);
            stmt.setString(8, topic);
            stmt.setString(9, grade);
            stmt.setString(10, difficulty);
            stmt.setString(11, source);
            stmt.setString(12, imagesJson != null ? imagesJson : "[]");
            stmt.setString(13, stepsJson != null ? stepsJson : "[]");
            stmt.setString(14, metadataJson != null ? metadataJson : "{}");
            stmt.setString(15, vectorToString(embedding.vectorAsList()));

            stmt.executeUpdate();
            log.debug("✅ 数学知识已添加: {}", id);
            return id;

        } catch (SQLException e) {
            log.error("❌ 添加数学知识失败: {}", e.getMessage());
            throw new RuntimeException("添加数学知识到数据库失败", e);
        }
    }

    /**
     * 检查 math_rag_knowledge 表中的向量数量
     */
    public long getVectorCount() {
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement("SELECT COUNT(*) FROM math_rag_knowledge")) {

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getLong(1);
            }

        } catch (SQLException e) {
            log.error("❌ 获取数学知识向量数量失败: {}", e.getMessage());
        }
        return 0;
    }

    /**
     * 检查指定文件是否已存在数学知识向量数据
     */
    public boolean hasMathKnowledgeForFile(String fileName) {
        if (fileName == null) return false;

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(
                     "SELECT COUNT(*) FROM math_rag_knowledge WHERE metadata->>'file_name' = ?")) {

            stmt.setString(1, fileName);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getLong(1) > 0;
            }

        } catch (SQLException e) {
            log.error("❌ 检查数学知识文件向量数据失败: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 检查指定内容哈希是否已存在
     */
    public boolean hasVectorForContentHash(String contentHash) {
        if (contentHash == null) return false;

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(
                     "SELECT COUNT(*) FROM math_rag_knowledge WHERE metadata->>'content_hash' = ?")) {

            stmt.setString(1, contentHash);
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getLong(1) > 0;
            }

        } catch (SQLException e) {
            log.error("❌ 检查内容哈希失败: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 获取所有已存在的文件名列表
     */
    public List<String> getExistingFileNames() {
        List<String> fileNames = new ArrayList<>();

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(
                     "SELECT DISTINCT metadata->>'file_name' FROM math_rag_knowledge WHERE metadata->>'file_name' IS NOT NULL")) {

            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                fileNames.add(rs.getString(1));
            }

        } catch (SQLException e) {
            log.error("❌ 获取现有文件名列表失败: {}", e.getMessage());
        }
        return fileNames;
    }

    /**
     * 删除指定文件的所有数学知识向量数据
     */
    public void removeMathKnowledgeForFile(String fileName) {
        if (fileName == null) return;

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(
                     "DELETE FROM math_rag_knowledge WHERE metadata->>'file_name' = ?")) {

            stmt.setString(1, fileName);
            int deletedCount = stmt.executeUpdate();
            log.info("🗑️ 已删除文件 {} 的 {} 个数学知识向量", fileName, deletedCount);

        } catch (SQLException e) {
            log.error("❌ 删除文件数学知识向量数据失败: {}", e.getMessage());
            throw new RuntimeException("删除文件数学知识向量数据失败", e);
        }
    }

    /**
     * 将向量转换为PostgreSQL可识别的字符串格式
     */
    private String vectorToString(List<Float> vector) {
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < vector.size(); i++) {
            if (i > 0) sb.append(",");
            sb.append(vector.get(i));
        }
        sb.append("]");
        return sb.toString();
    }
}
