package com.hujun.aicodehelper.ai;

import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.rag.content.Content;
import dev.langchain4j.rag.content.retriever.ContentRetriever;
import dev.langchain4j.rag.query.Query;
import dev.langchain4j.service.Result;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingStore;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * RAG (Retrieval Augmented Generation) 专用服务
 * 提供知识库检索、文档搜索等功能
 */
@Slf4j
@Service
public class RagService {

    @Resource
    private EmbeddingModel embeddingModel;

    @Resource
    private EmbeddingStore<TextSegment> embeddingStore;

    @Resource
    private ContentRetriever contentRetriever;

    @Resource
    private AiCodeHelperService aiCodeHelperService;

    /**
     * 直接搜索知识库
     * @param query 搜索查询
     * @param maxResults 最大结果数
     * @param minScore 最小相似度分数
     * @return 匹配的文档片段
     */
    public List<RagSearchResult> searchKnowledge(String query, int maxResults, double minScore) {
        log.info("🔍 开始搜索知识库 - 查询: '{}', 最大结果: {}, 最小分数: {}", query, maxResults, minScore);
        
        // 使用ContentRetriever来检索相关内容
        Query ragQuery = Query.from(query);
        log.debug("📝 构建查询对象: {}", ragQuery);
        
        log.info("🚀 正在调用当前配置的嵌入模型进行查询向量化...");
        long startTime = System.currentTimeMillis();
        List<Content> contents = contentRetriever.retrieve(ragQuery);
        long endTime = System.currentTimeMillis();
        
        log.info("✅ 向量检索完成！耗时: {} 毫秒，检索到 {} 个结果", endTime - startTime, contents.size());
        
        // 打印检索到的内容摘要
        for (int i = 0; i < contents.size() && i < 5; i++) {
            Content content = contents.get(i);
            String fileName = content.textSegment().metadata().getString("file_name");
            String preview = content.textSegment().text().substring(0, Math.min(100, content.textSegment().text().length()));
            log.info("📄 结果 {}: {} - {}", i + 1, fileName, preview + "...");
        }
        
        // 转换为结果对象，这里模拟评分（实际评分需要从Content中获取，如果可用的话）
        List<RagSearchResult> results = contents.stream()
                .limit(maxResults)
                .map(content -> new RagSearchResult(
                        content.textSegment().text(),
                        0.85, // 使用固定评分，实际应用中可以从content获取
                        content.textSegment().metadata().getString("file_name"),
                        content.textSegment().metadata().toMap()
                ))
                .collect(Collectors.toList());
        
        log.info("🎯 搜索完成，返回 {} 个结果", results.size());
        return results;
    }

    /**
     * RAG增强问答
     * @param question 用户问题
     * @return 包含答案和来源的结果
     */
    public RagChatResult chatWithRag(String question) {
        log.info("💬 开始 RAG 增强问答 - 问题: '{}'", question);
        
        log.info("🚀 正在调用 text-embedding-004 模型进行问题向量化和知识库检索...");
        long startTime = System.currentTimeMillis();
        Result<String> result = aiCodeHelperService.chatWithRag(question);
        long endTime = System.currentTimeMillis();
        
        log.info("✅ RAG 问答完成！总耗时: {} 毫秒", endTime - startTime);
        log.info("📝 生成答案长度: {} 字符", result.content().length());
        log.info("📚 使用知识源数量: {}", result.sources().size());
        
        // 打印使用的知识源
        for (int i = 0; i < result.sources().size(); i++) {
            Content content = result.sources().get(i);
            String fileName = content.textSegment().metadata().getString("file_name");
            log.info("📄 知识源 {}: {}", i + 1, fileName);
        }
        
        // 提取源文档信息
        List<RagSource> sources = result.sources().stream()
                .map(content -> new RagSource(
                        content.textSegment().text(),
                        content.textSegment().metadata().getString("file_name"),
                        content.textSegment().metadata().toMap()
                ))
                .collect(Collectors.toList());

        log.info("🎉 RAG 问答结果构建完成");
        return new RagChatResult(result.content(), sources);
    }

    /**
     * 获取知识库统计信息
     * @return 知识库统计
     */
    public KnowledgeStats getKnowledgeStats() {
        String storeType;
        long documentCount = 4; // 默认文档数量
        
        // 检查是否使用 PostgreSQL 向量存储
        if (embeddingStore instanceof com.hujun.aicodehelper.ai.model.PostgresVectorStore) {
            storeType = "PostgreSQL 向量数据库 (pgvector)";
            try {
                com.hujun.aicodehelper.ai.model.PostgresVectorStore postgresStore = 
                    (com.hujun.aicodehelper.ai.model.PostgresVectorStore) embeddingStore;
                documentCount = postgresStore.getVectorCount();
                log.info("📊 从 PostgreSQL 获取到 {} 个向量", documentCount);
            } catch (Exception e) {
                log.warn("⚠️ 无法获取 PostgreSQL 向量数量: {}", e.getMessage());
            }
        } else {
            storeType = "内存向量数据库";
            log.info("📊 使用内存向量数据库，默认文档数量: {}", documentCount);
        }

        return new KnowledgeStats(
                storeType,
                "包含Java学习路线、面试题、求职指南、项目建议等知识文档，支持持久化存储和高性能向量检索",
                (int) documentCount,
                embeddingModel.getClass().getSimpleName()
        );
    }

    /**
     * RAG搜索结果
     */
    public static class RagSearchResult {
        private String text;
        private double score;
        private String fileName;
        private Object metadata;

        public RagSearchResult(String text, double score, String fileName, Object metadata) {
            this.text = text;
            this.score = score;
            this.fileName = fileName;
            this.metadata = metadata;
        }

        // Getters
        public String getText() { return text; }
        public double getScore() { return score; }
        public String getFileName() { return fileName; }
        public Object getMetadata() { return metadata; }
    }

    /**
     * RAG聊天结果
     */
    public static class RagChatResult {
        private String answer;
        private List<RagSource> sources;

        public RagChatResult(String answer, List<RagSource> sources) {
            this.answer = answer;
            this.sources = sources;
        }

        // Getters
        public String getAnswer() { return answer; }
        public List<RagSource> getSources() { return sources; }
    }

    /**
     * RAG源文档
     */
    public static class RagSource {
        private String text;
        private String fileName;
        private Object metadata;

        public RagSource(String text, String fileName, Object metadata) {
            this.text = text;
            this.fileName = fileName;
            this.metadata = metadata;
        }

        // Getters
        public String getText() { return text; }
        public String getFileName() { return fileName; }
        public Object getMetadata() { return metadata; }
    }

    /**
     * 知识库统计信息
     */
    public static class KnowledgeStats {
        private String storeType;
        private String description;
        private int documentCount;
        private String embeddingModel;

        public KnowledgeStats(String storeType, String description, int documentCount, String embeddingModel) {
            this.storeType = storeType;
            this.description = description;
            this.documentCount = documentCount;
            this.embeddingModel = embeddingModel;
        }

        // Getters
        public String getStoreType() { return storeType; }
        public String getDescription() { return description; }
        public int getDocumentCount() { return documentCount; }
        public String getEmbeddingModel() { return embeddingModel; }
    }
} 