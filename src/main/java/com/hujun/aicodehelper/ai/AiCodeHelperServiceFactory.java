package com.hujun.aicodehelper.ai;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import com.hujun.aicodehelper.ai.tools.CambridgeDictionaryTool;

import dev.langchain4j.mcp.McpToolProvider;
import dev.langchain4j.memory.chat.ChatMemoryProvider;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.StreamingChatModel;
import dev.langchain4j.rag.content.retriever.ContentRetriever;
import dev.langchain4j.service.AiServices;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
public class AiCodeHelperServiceFactory {

    @Value("${ai.chat.memory.debug-mode:false}")
    private boolean debugMode;

    @Resource
    private ChatModel myGeminiChatModel;

    @Resource
    private StreamingChatModel myGeminiStreamingChatModel;

    @Resource
    private ContentRetriever contentRetriever;

    @Resource
    private McpToolProvider mcpToolProvider;

    @Resource
    private ChatMemoryProvider chatMemoryProvider;

    // 注释掉旧的Bean，使用SmartAiCodeHelperService替代
    // @Bean
    public AiCodeHelperService legacyAiCodeHelperService() {
        // 根据调试模式选择聊天记忆提供者
        ChatMemoryProvider memoryProvider;
        if (debugMode) {
            log.warn("🚨 调试模式已启用 - 使用简单内存聊天记忆（不持久化）");
            memoryProvider = memoryId -> MessageWindowChatMemory.withMaxMessages(5); // 调试时减少消息数量
        } else {
            log.info("✅ 生产模式 - 使用PostgreSQL持久化聊天记忆");
            memoryProvider = chatMemoryProvider;
        }

        // 构造 AI Service - 只使用StreamingChatModel确保流式响应正常工作
        log.info("🔧 构建AI服务，强制使用StreamingChatModel");
        AiCodeHelperService aiCodeHelperService = AiServices.builder(AiCodeHelperService.class)
                // 只使用StreamingChatModel，确保流式响应正常工作
                .streamingChatModel(myGeminiStreamingChatModel)
                .chatMemoryProvider(memoryProvider)
                .contentRetriever(contentRetriever) // RAG 检索增强生成
                .tools(new CambridgeDictionaryTool()) // 剑桥词典查询工具
                // .toolProvider(mcpToolProvider) // 暂时移除MCP工具调用，避免与直接工具注册冲突
                .build();
        return aiCodeHelperService;
    }
}