package com.hujun.aicodehelper.ai;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.stereotype.Service;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;

import com.hujun.aicodehelper.ai.guardrail.SafeInputGuardrail;
import com.hujun.aicodehelper.ai.strategy.ModelSelectionStrategy;
import com.hujun.aicodehelper.ai.strategy.ModelSelectionStrategy.ModelType;

import dev.langchain4j.data.image.Image;
import dev.langchain4j.data.message.Content;
import dev.langchain4j.data.message.ImageContent;
import dev.langchain4j.data.message.TextContent;
import dev.langchain4j.service.Result;
import dev.langchain4j.service.guardrail.InputGuardrails;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

/**
 * 智能AI代码助手服务
 * 实现动态模型切换，根据请求自动选择最合适的模型
 */
@Slf4j
@Service("aiCodeHelperService") // 明确指定Bean名称
@InputGuardrails({ SafeInputGuardrail.class })
public class SmartAiCodeHelperService implements AiCodeHelperService {

    @Resource
    private DynamicAiServiceManager dynamicAiServiceManager;

    @Resource
    private ModelSelectionStrategy modelSelectionStrategy;

    // 会话记忆缓存，用于跟踪对话历史
    private final Map<Integer, List<String>> conversationHistory = new ConcurrentHashMap<>();

    // 工具调用监听器，用于检测工具使用情况
    private final Map<Integer, String> lastToolUsed = new ConcurrentHashMap<>();

    @Override
    public String chat(String userMessage) {
        log.info("收到聊天请求: {}", userMessage.substring(0, Math.min(50, userMessage.length())));

        // 获取合适的AI服务
        AiCodeHelperService aiService = dynamicAiServiceManager.getAiService(userMessage, null);

        // 执行聊天
        return aiService.chat(userMessage);
    }

    @Override
    public Flux<String> chatStream(int memoryId, List<Content> contents) {
        // 为模型选择拼接一个可读摘要（不影响传递给模型的多模态内容）
        StringBuilder summary = new StringBuilder();
        for (Content c : contents) {
            if (c instanceof TextContent tc) {
                String t = tc.text();
                if (t != null) summary.append(t);
            } else if (c instanceof ImageContent) {
                summary.append(" [image]");
            }
        }

        List<String> history = getConversationHistory(memoryId);
        ModelType selectedModelType = optimizeModelSelection(summary.toString(), history, memoryId);
        AiCodeHelperService aiService = dynamicAiServiceManager.getAiServiceByModelType(selectedModelType);

        updateConversationHistory(memoryId, summary.toString());
        log.info("多模态内容块会话选择模型: {}", selectedModelType);

        return aiService.chatStream(memoryId, contents)
                .doOnNext(chunk -> {
                    log.info("🎯 chatStream(contents) 数据块，会话: {}, 长度: {}", memoryId, chunk != null ? chunk.length() : 0);
                    detectToolUsageInResponse(memoryId, chunk);
                })
                .doOnComplete(() -> log.info("✅ chatStream(contents) 完成，会话: {}", memoryId))
                .doOnError(err -> log.error("❌ chatStream(contents) 错误，会话: {}, 错误: {}", memoryId, err.getMessage(), err));
    }

    @Override
    public Report chatForReport(String userMessage) {
        log.info("收到报告生成请求: {}", userMessage.substring(0, Math.min(50, userMessage.length())));

        // 报告生成通常需要更强的模型
        AiCodeHelperService aiService = dynamicAiServiceManager.getAiServiceByModelType(ModelType.PRO);

        return aiService.chatForReport(userMessage);
    }

    @Override
    public Result<String> chatWithRag(String userMessage) {
        log.info("收到RAG聊天请求: {}", userMessage.substring(0, Math.min(50, userMessage.length())));

        // RAG查询使用专门的RAG模型
        AiCodeHelperService aiService = dynamicAiServiceManager.getAiServiceByModelType(ModelType.RAG);

        return aiService.chatWithRag(userMessage);
    }

    @Override
    public Flux<String> chatStream(int memoryId, String userMessage) {
        log.info("收到流式聊天请求 [会话:{}]: {}", memoryId, userMessage.substring(0, Math.min(50, userMessage.length())));

        // 获取该会话的历史记录
        List<String> history = getConversationHistory(memoryId);

        // 智能选择模型类型（考虑消息内容、历史记录和工具使用）
        ModelType selectedModelType = optimizeModelSelection(userMessage, history, memoryId);

        // 获取合适的AI服务
        AiCodeHelperService aiService = dynamicAiServiceManager.getAiServiceByModelType(selectedModelType);

        // 更新会话历史
        updateConversationHistory(memoryId, userMessage);

        log.info("为会话 {} 选择模型: {}", memoryId, selectedModelType.getDescription());

        return aiService.chatStream(memoryId, userMessage)
                .doOnNext(chunk -> {
                    log.info("🎯 SmartAiCodeHelperService收到流式数据块，会话: {}, 长度: {}, 内容: [{}]",
                            memoryId,
                            chunk != null ? chunk.length() : 0,
                            chunk != null ? chunk.substring(0, Math.min(100, chunk.length())) : "null");
                    // 监听响应中的工具调用
                    detectToolUsageInResponse(memoryId, chunk);
                })
                .doOnComplete(() -> {
                    log.info("✅ SmartAiCodeHelperService流式响应完成，会话: {}", memoryId);
                })
                .doOnError(error -> {
                    log.error("❌ SmartAiCodeHelperService流式响应出错，会话: {}, 错误: {}", memoryId, error.getMessage(), error);
                });
    }

    @Override
    public Flux<String> chatStreamWithImage(int memoryId, String textMessage, String imageUrl) {
        log.info("收到多模态流式聊天请求 [会话:{}]: 文本={}, 图片={}", 
                memoryId, 
                textMessage.substring(0, Math.min(50, textMessage.length())),
                imageUrl != null ? "有图片" : "无图片");
        
        // 获取该会话的历史记录
        List<String> history = getConversationHistory(memoryId);
        String combinedMessage = textMessage;

        // 如果包含图片，优先尝试使用内容块形式传递图片
        if (imageUrl != null && !imageUrl.trim().isEmpty()) {
            try {
                List<Content> contents = new ArrayList<>();
                contents.add(TextContent.from(textMessage));

                // 将URL转换为本地文件路径并读取二进制
                Image image = loadImageFromLocalUrl(imageUrl);
                if (image != null) {
                    contents.add(ImageContent.from(image));

                    // 按你的要求：含图场景也使用最便宜的 flash-lite
                    ModelType selectedModelType = ModelType.LITE;
                    AiCodeHelperService aiService = dynamicAiServiceManager.getAiServiceByModelType(selectedModelType);

                    // 为了对齐 Studio 的使用习惯，这里仅做轻微提示，不改变模型
                    String ocrHint = textMessage + "\n\n请先读取图片文字再进行解题。";
                    contents.set(0, TextContent.from(ocrHint));

                    updateConversationHistory(memoryId, textMessage + " [附带图片]");
                    log.info("为多模态会话 {} 选择模型: {} (使用内容块/flash-lite)", memoryId, selectedModelType);

                    return aiService.chatStream(memoryId, contents)
                            .doOnNext(chunk -> {
                                log.info("🎯 多模态(内容块)流式数据块，会话: {}, 长度: {}, 内容: [{}]",
                                        memoryId,
                                        chunk != null ? chunk.length() : 0,
                                        chunk != null ? chunk.substring(0, Math.min(100, chunk.length())) : "null");
                                detectToolUsageInResponse(memoryId, chunk);
                            })
                            .doOnComplete(() -> log.info("✅ 多模态(内容块)流式响应完成，会话: {}", memoryId))
                            .doOnError(error -> log.error("❌ 多模态(内容块)异常，会话: {}, 错误: {}", memoryId, error.getMessage(), error));
                } else {
                    log.warn("⚠️ 未能加载图片内容，回退为纯文本对话");
                }
            } catch (Exception ex) {
                log.error("读取或构造图片内容失败，回退文本: {}", ex.getMessage(), ex);
            }
            // 如果失败则回退到在文本中描述图片
            combinedMessage = textMessage + "\n\n[用户上传了一张图片: " + imageUrl + "]\n请基于这张图片来回答用户的问题。";
        }

        // 文本模式：智能选择模型并继续
        ModelType selectedModelType = optimizeModelSelection(combinedMessage, history, memoryId);
        AiCodeHelperService aiService = dynamicAiServiceManager.getAiServiceByModelType(selectedModelType);
        updateConversationHistory(memoryId, combinedMessage);
        log.info("为多模态会话 {} 选择模型: {} (文本回退)", memoryId, selectedModelType);

        return aiService.chatStream(memoryId, combinedMessage)
                .doOnNext(chunk -> {
                    log.info("🎯 SmartAiCodeHelperService收到多模态流式数据块，会话: {}, 长度: {}, 内容: [{}]",
                            memoryId,
                            chunk != null ? chunk.length() : 0,
                            chunk != null ? chunk.substring(0, Math.min(100, chunk.length())) : "null");
                    // 监听响应中的工具调用
                    detectToolUsageInResponse(memoryId, chunk);
                })
                .doOnComplete(() -> {
                    log.info("✅ SmartAiCodeHelperService多模态流式响应完成，会话: {}", memoryId);
                })
                .doOnError(error -> {
                    log.error("❌ SmartAiCodeHelperService多模态流式聊天异常，会话: {}, 错误: {}", memoryId, error.getMessage(), error);
                });
    }

    private Image loadImageFromLocalUrl(String imageUrl) {
        try {
            // 期望形如：/api/mobile-upload/file/yyyy/mm/dd/filename.ext
            String prefix = "/api/mobile-upload/file/";
            if (!imageUrl.startsWith(prefix)) {
                log.warn("无法识别的图片URL前缀: {}", imageUrl);
                return null;
            }
            String relative = imageUrl.substring(prefix.length());
            Path filePath = Paths.get("data/uploads").resolve(relative);
            byte[] bytes = Files.readAllBytes(filePath);

            // 先按扩展名判断
            String mime = guessMimeTypeByExtension(filePath.getFileName().toString());
            // 其次尝试基于操作系统探测
            if (mime == null) {
                try {
                    mime = Files.probeContentType(filePath);
                } catch (Exception ignore) {}
            }
            // 取消对 ImageInputStream 的依赖以避免某些最小化 JDK 缺少类导致编译失败
            // 兜底：与常见拍照格式兼容，默认使用 JPEG
            if (mime == null) mime = "image/jpeg";

            // 构造LangChain4j Image对象（使用base64数据）
            String b64 = Base64.getEncoder().encodeToString(bytes);
            return Image.builder()
                    .base64Data(b64)
                    .mimeType(mime)
                    .build();
        } catch (Exception e) {
            log.error("读取图片失败: {}", e.getMessage(), e);
            return null;
        }
    }

    private String guessMimeTypeByExtension(String filename) {
        String lower = filename.toLowerCase();
        if (lower.endsWith(".jpg") || lower.endsWith(".jpeg")) return "image/jpeg";
        if (lower.endsWith(".png")) return "image/png";
        if (lower.endsWith(".gif")) return "image/gif";
        if (lower.endsWith(".webp")) return "image/webp";
        return null;
    }

    /**
     * 获取可用的模型信息
     */
    public Map<String, Object> getModelInfo() {
        Map<String, Object> info = new ConcurrentHashMap<>();

        // 模型可用性
        Map<ModelType, Boolean> availability = dynamicAiServiceManager.getModelAvailability();
        info.put("modelAvailability", availability);

        // 可用模型类型
        List<ModelType> availableTypes = dynamicAiServiceManager.getAvailableModelTypes();
        info.put("availableModels", availableTypes);

        // 当前活跃的会话数
        info.put("activeSessions", conversationHistory.size());

        return info;
    }

    /**
     * 强制切换指定会话的模型
     */
    public void forceModelSwitch(int memoryId, ModelType modelType) {
        log.info("强制切换会话 {} 的模型为: {}", memoryId, modelType.getDescription());
        lastToolUsed.put(memoryId, "FORCE_SWITCH_" + modelType.name());
    }

    /**
     * 清理会话历史
     */
    public void clearSessionHistory(int memoryId) {
        conversationHistory.remove(memoryId);
        lastToolUsed.remove(memoryId);
        log.info("清理会话历史: {}", memoryId);
    }

    /**
     * 预热所有模型
     */
    public void warmUpAllModels() {
        log.info("开始预热所有AI模型...");
        dynamicAiServiceManager.warmUpServices();
    }

    /**
     * 获取会话的对话历史
     */
    private List<String> getConversationHistory(int memoryId) {
        return conversationHistory.computeIfAbsent(memoryId, k -> new ArrayList<>());
    }

    /**
     * 更新会话的对话历史
     */
    private void updateConversationHistory(int memoryId, String userMessage) {
        List<String> history = getConversationHistory(memoryId);
        history.add(userMessage);

        // 限制历史记录长度，避免内存溢出
        if (history.size() > 20) {
            history.remove(0);
        }
    }

    /**
     * 检测消息应该使用的模型类型
     */
    private ModelType detectModelTypeForMessage(String userMessage, List<String> history) {
        // 使用模型选择策略进行智能检测
        return modelSelectionStrategy.selectModel(userMessage, history);
    }

    /**
     * 检测响应中的工具调用
     */
    private void detectToolUsageInResponse(int memoryId, String responseChunk) {
        // 检测响应中是否包含工具调用的标识
        if (responseChunk.contains("dictionarySearch") ||
                responseChunk.contains("Cambridge Dictionary") ||
                responseChunk.contains("词典查询")) {
            lastToolUsed.put(memoryId, "dictionarySearch");
        }
    }

    /**
     * 检测工具使用并更新模型选择
     */
    private void handleToolUsage(int memoryId, String toolName) {
        lastToolUsed.put(memoryId, toolName);

        // 基于工具使用情况，可以为下次对话预选模型
        if ("dictionarySearch".equals(toolName)) {
            log.debug("会话 {} 使用了词典工具，建议后续使用词典模型", memoryId);
        }
    }

    /**
     * 根据会话历史和工具使用情况优化模型选择
     */
    private ModelType optimizeModelSelection(String userMessage, List<String> history, int memoryId) {
        // 首先使用基础策略
        ModelType baseSelection = modelSelectionStrategy.selectModel(userMessage, history);

        // 考虑最近的工具使用情况
        String lastTool = lastToolUsed.get(memoryId);
        if (lastTool != null) {
            ModelType toolBasedSelection = modelSelectionStrategy.selectModelForTool(lastTool);

            // 如果基础选择是LITE，但最近使用了特殊工具，可能需要调整
            if (baseSelection == ModelType.LITE && toolBasedSelection != ModelType.LITE) {
                log.debug("基于工具使用历史，将模型从 {} 调整为 {}", baseSelection, toolBasedSelection);
                return toolBasedSelection;
            }
        }

        return baseSelection;
    }
}
