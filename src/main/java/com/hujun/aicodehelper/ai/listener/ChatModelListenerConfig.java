package com.hujun.aicodehelper.ai.listener;

import dev.langchain4j.model.chat.listener.ChatModelErrorContext;
import dev.langchain4j.model.chat.listener.ChatModelListener;
import dev.langchain4j.model.chat.listener.ChatModelRequestContext;
import dev.langchain4j.model.chat.listener.ChatModelResponseContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class ChatModelListenerConfig {
    
    @Bean
    ChatModelListener chatModelListener() {
        return new ChatModelListener() {
            @Override
            public void onRequest(ChatModelRequestContext requestContext) {
                log.info("🔄 onRequest(): {}", requestContext.chatRequest());
                
                // 详细分析请求消息
                var messages = requestContext.chatRequest().messages();
                log.info("📨 消息总数: {}", messages.size());
                for (int i = 0; i < messages.size(); i++) {
                    var message = messages.get(i);
                    String messageText = getMessageText(message);
                    log.info("📝 消息[{}]: 类型={}, 内容长度={}", 
                        i, message.getClass().getSimpleName(), 
                        messageText != null ? messageText.length() : 0);
                        
                    // 特别关注工具相关消息
                    if (message instanceof dev.langchain4j.data.message.AiMessage) {
                        var aiMsg = (dev.langchain4j.data.message.AiMessage) message;
                        if (aiMsg.hasToolExecutionRequests()) {
                            log.info("🔧 AI消息包含工具调用请求: 数量={}", 
                                aiMsg.toolExecutionRequests().size());
                        }
                    } else if (message instanceof dev.langchain4j.data.message.ToolExecutionResultMessage) {
                        var toolMsg = (dev.langchain4j.data.message.ToolExecutionResultMessage) message;
                        log.info("🛠️ 工具执行结果消息: 工具名={}, 结果长度={}", 
                            toolMsg.toolName(), toolMsg.text() != null ? toolMsg.text().length() : 0);
                    }
                }
            }

            @Override
            public void onResponse(ChatModelResponseContext responseContext) {
                log.info("✅ onResponse(): {}", responseContext.chatResponse());
                
                var response = responseContext.chatResponse();
                if (response.aiMessage().hasToolExecutionRequests()) {
                    log.info("🔧 响应包含工具调用请求: 数量={}", 
                        response.aiMessage().toolExecutionRequests().size());
                }
                
                log.info("📊 Token使用情况: {}", response.metadata().tokenUsage());
                log.info("🏁 完成原因: {}", response.metadata().finishReason());
            }

            @Override
            public void onError(ChatModelErrorContext errorContext) {
                log.error("❌ onError(): {}", errorContext.error().getMessage());
                log.error("🔍 错误详情: ", errorContext.error());
                
                // 打印错误时的请求内容以便调试
                if (errorContext.chatRequest() != null) {
                    var messages = errorContext.chatRequest().messages();
                    log.error("📨 错误时的消息数量: {}", messages.size());
                    for (int i = 0; i < messages.size(); i++) {
                        var message = messages.get(i);
                        log.error("📝 错误消息[{}]: 类型={}", i, message.getClass().getSimpleName());
                    }
                }
            }
            
            // 辅助方法：获取消息文本内容
            private String getMessageText(dev.langchain4j.data.message.ChatMessage message) {
                if (message instanceof dev.langchain4j.data.message.UserMessage) {
                    return ((dev.langchain4j.data.message.UserMessage) message).singleText();
                } else if (message instanceof dev.langchain4j.data.message.AiMessage) {
                    return ((dev.langchain4j.data.message.AiMessage) message).text();
                } else if (message instanceof dev.langchain4j.data.message.SystemMessage) {
                    return ((dev.langchain4j.data.message.SystemMessage) message).text();
                } else if (message instanceof dev.langchain4j.data.message.ToolExecutionResultMessage) {
                    return ((dev.langchain4j.data.message.ToolExecutionResultMessage) message).text();
                }
                return null;
            }
        };
    }
}