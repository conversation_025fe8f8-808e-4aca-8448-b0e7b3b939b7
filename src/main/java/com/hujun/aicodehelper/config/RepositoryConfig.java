package com.hujun.aicodehelper.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * Repository 配置类
 * 配置不同数据源的 JPA Repository
 */
@Configuration
public class RepositoryConfig {

    /**
     * MySQL Repository 配置
     * 用于聊天记忆相关的数据访问
     */
    /*
    @Configuration
    @EnableJpaRepositories(
            basePackages = "com.hujun.aicodehelper.chat.repository",
            entityManagerFactoryRef = "mysqlEntityManagerFactory",
            transactionManagerRef = "mysqlTransactionManager"
    )
    public static class MySQLRepositoryConfig {
    }
    */

    /**
     * PostgreSQL Repository 配置 (可选)
     * 向量数据库主要使用原生SQL，暂时不需要JPA Repository
     */
    @Configuration
    @EnableJpaRepositories(
            basePackages = {"com.hujun.aicodehelper.chat.repository", "com.hujun.aicodehelper.ai.repository"},
            entityManagerFactoryRef = "postgresqlEntityManagerFactory",
            transactionManagerRef = "postgresqlTransactionManager"
    )
    public static class PostgreSQLRepositoryConfig {
    }
}