package com.hujun.aicodehelper.controller;

import com.hujun.aicodehelper.ai.tools.CambridgeDictionaryTool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 字典查询专用控制器
 * 完全绕过AI模型，直接返回原始数据，确保格式完整性
 */
@Slf4j
@RestController
@RequestMapping("/dictionary")
public class DictionaryController {

    private final CambridgeDictionaryTool dictionaryTool;

    public DictionaryController(CambridgeDictionaryTool dictionaryTool) {
        this.dictionaryTool = dictionaryTool;
    }

    /**
     * 直接查询单词 - 完全绕过AI处理
     * 返回原始格式的词典数据，包括完整的音标
     */
    @GetMapping("/search")
    public Map<String, Object> searchWord(@RequestParam String word) {
        log.info("直接查询单词: {}", word);
        
        try {
            // 直接调用工具方法，不经过AI模型
            String result = dictionaryTool.searchWordDirect(word);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("word", word);
            response.put("result", result);
            response.put("timestamp", System.currentTimeMillis());
            
            log.info("单词 '{}' 查询成功，结果长度: {}", word, result.length());
            return response;
            
        } catch (Exception e) {
            log.error("查询单词 '{}' 时发生错误", word, e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("word", word);
            response.put("error", e.getMessage());
            response.put("timestamp", System.currentTimeMillis());
            
            return response;
        }
    }

    /**
     * 批量查询单词
     */
    @PostMapping("/batch-search")
    public Map<String, Object> batchSearchWords(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        var words = (java.util.List<String>) request.get("words");
        
        log.info("批量查询单词: {}", words);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("results", new HashMap<String, String>());
        response.put("timestamp", System.currentTimeMillis());
        
        @SuppressWarnings("unchecked")
        var results = (Map<String, String>) response.get("results");
        
        for (String word : words) {
            try {
                String result = dictionaryTool.searchWordDirect(word);
                results.put(word, result);
            } catch (Exception e) {
                log.error("批量查询单词 '{}' 时发生错误", word, e);
                results.put(word, "查询失败: " + e.getMessage());
            }
        }
        
        return response;
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "Dictionary Service");
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
}
