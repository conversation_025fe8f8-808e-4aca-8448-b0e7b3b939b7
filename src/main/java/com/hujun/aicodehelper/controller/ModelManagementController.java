package com.hujun.aicodehelper.controller;

import com.hujun.aicodehelper.ai.DynamicAiServiceManager;
import com.hujun.aicodehelper.ai.model.MultiGeminiModelConfig;
import com.hujun.aicodehelper.ai.strategy.ModelSelectionStrategy.ModelType;
// Swagger注解暂时移除，避免依赖问题
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 模型管理控制器
 * 提供模型配置查看、切换等功能的REST API
 */
@Slf4j
@RestController
@RequestMapping("/models")
public class ModelManagementController {

    @Resource
    private DynamicAiServiceManager dynamicAiServiceManager;

    @Resource
    private MultiGeminiModelConfig multiGeminiModelConfig;

    /**
     * 获取所有可用模型信息
     */
    @GetMapping("/info")
    public Map<String, Object> getModelInfo() {
        log.info("获取模型信息请求");
        
        Map<String, Object> result = new HashMap<>();
        
        // 从配置获取模型详细信息
        Map<String, Object> modelDetails = multiGeminiModelConfig.getModelInfo();
        result.putAll(modelDetails);
        
        // 从管理器获取服务状态
        Map<String, Object> serviceStats = dynamicAiServiceManager.getServiceStats();
        result.put("serviceStats", serviceStats);
        
        // 模型可用性
        Map<ModelType, Boolean> availability = dynamicAiServiceManager.getModelAvailability();
        result.put("availability", availability);
        
        return result;
    }

    /**
     * 获取模型使用统计
     */
    @GetMapping("/stats")
    public Map<String, Object> getModelStats() {
        log.info("获取模型统计信息请求");
        return dynamicAiServiceManager.getServiceStats();
    }

    /**
     * 刷新模型服务缓存
     */
    @PostMapping("/refresh")
    public Map<String, Object> refreshModels() {
        log.info("刷新模型服务请求");
        
        try {
            dynamicAiServiceManager.refreshServices();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "模型服务刷新成功");
            result.put("timestamp", System.currentTimeMillis());
            
            return result;
        } catch (Exception e) {
            log.error("刷新模型服务失败", e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "模型服务刷新失败: " + e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
            
            return result;
        }
    }

    /**
     * 测试特定模型
     */
    @PostMapping("/test/{modelType}")
    public Map<String, Object> testModel(@PathVariable ModelType modelType, 
                                        @RequestParam(defaultValue = "Hello") String testMessage) {
        log.info("测试模型请求: {}, 消息: {}", modelType, testMessage);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取指定模型的服务
            var aiService = dynamicAiServiceManager.getAiServiceByModelType(modelType);
            
            // 执行简单测试
            long startTime = System.currentTimeMillis();
            String response = aiService.chat(testMessage);
            long endTime = System.currentTimeMillis();
            
            result.put("success", true);
            result.put("modelType", modelType.name());
            result.put("testMessage", testMessage);
            result.put("response", response);
            result.put("responseTime", endTime - startTime);
            result.put("timestamp", startTime);
            
        } catch (Exception e) {
            log.error("测试模型 {} 失败", modelType, e);
            
            result.put("success", false);
            result.put("modelType", modelType.name());
            result.put("error", e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
        }
        
        return result;
    }

    /**
     * 获取模型选择建议
     */
    @PostMapping("/suggest")
    public Map<String, Object> suggestModel(@RequestBody Map<String, Object> request) {
        String userMessage = (String) request.get("message");
        log.info("模型选择建议请求: {}", userMessage != null ? userMessage.substring(0, Math.min(50, userMessage.length())) : "null");
        
        Map<String, Object> result = new HashMap<>();
        
        if (userMessage == null || userMessage.trim().isEmpty()) {
            result.put("success", false);
            result.put("message", "请提供用户消息");
            return result;
        }
        
        try {
            // 获取合适的服务
            var aiService = dynamicAiServiceManager.getAiService(userMessage, null);
            
            // 这里可以添加更复杂的逻辑来确定使用了哪个模型
            // 目前返回基本信息
            result.put("success", true);
            result.put("userMessage", userMessage);
            result.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("获取模型建议失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
}