package com.hujun.aicodehelper.services;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.UnknownHostException;

import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class NetworkTestService {

    /**
     * 测试Azure语音服务的网络连接
     */
    public boolean testAzureSpeechConnectivity(String region) {
        String[] endpoints = {
                "https://" + region + ".api.cognitive.microsoft.com",
                "https://" + region + ".tts.speech.microsoft.com",
                "wss://" + region + ".tts.speech.microsoft.com"
        };

        boolean allSuccess = true;

        for (String endpoint : endpoints) {
            try {
                if (endpoint.startsWith("wss://")) {
                    // 对于WebSocket端点，我们测试对应的HTTPS端点
                    String httpEndpoint = endpoint.replace("wss://", "https://");
                    testHttpConnection(httpEndpoint);
                } else {
                    testHttpConnection(endpoint);
                }
                log.info("✅ 连接成功: {}", endpoint);
            } catch (Exception e) {
                log.error("❌ 连接失败: {} - {}", endpoint, e.getMessage());
                allSuccess = false;
            }
        }

        return allSuccess;
    }

    /**
     * 测试本地 Ollama 服务的连通性
     */
    public boolean testOllamaConnectivity(String baseUrl) {
        String[] endpoints = {
                baseUrl,
                baseUrl + "/api/tags",
                baseUrl + "/api/generate"
        };

        boolean allSuccess = true;
        for (String endpoint : endpoints) {
            try {
                testHttpConnection(endpoint);
                log.info("✅ Ollama 连接成功: {}", endpoint);
            } catch (Exception e) {
                log.error("❌ Ollama 连接失败: {} - {}", endpoint, e.getMessage());
                allSuccess = false;
            }
        }

        return allSuccess;
    }

    private void testHttpConnection(String endpoint) throws IOException {
        try {
            URL url = new URL(endpoint);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000); // 5秒超时
            connection.setReadTimeout(5000);

            int responseCode = connection.getResponseCode();
            log.info("HTTP响应码: {} for {}", responseCode, endpoint);

            connection.disconnect();
        } catch (UnknownHostException e) {
            throw new IOException("DNS解析失败: " + endpoint, e);
        } catch (IOException e) {
            throw new IOException("连接失败: " + endpoint, e);
        }
    }

    /**
     * 检查系统代理和网络配置
     */
    public void checkNetworkConfig() {
        log.info("=== 网络配置检查 ===");

        // 检查系统代理
        String httpProxy = System.getProperty("http.proxyHost");
        String httpsProxy = System.getProperty("https.proxyHost");

        if (httpProxy != null) {
            log.info("HTTP代理: {}:{}", httpProxy, System.getProperty("http.proxyPort"));
        }
        if (httpsProxy != null) {
            log.info("HTTPS代理: {}:{}", httpsProxy, System.getProperty("https.proxyPort"));
        }

        if (httpProxy == null && httpsProxy == null) {
            log.info("未检测到系统代理配置");
        }

        // 检查DNS
        try {
            java.net.InetAddress.getByName("eastus.api.cognitive.microsoft.com");
            log.info("✅ DNS解析正常");
        } catch (Exception e) {
            log.error("❌ DNS解析失败: {}", e.getMessage());
        }
    }
}