package com.hujun.pdfparser.markdown.service;

import org.apache.tika.Tika;
import org.apache.tika.config.TikaConfig;
import org.apache.tika.exception.TikaException;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.parser.AutoDetectParser;
import org.apache.tika.parser.ParseContext;
import org.apache.tika.parser.Parser;
import org.apache.tika.parser.ocr.TesseractOCRConfig;
import org.apache.tika.parser.pdf.PDFParserConfig;
import org.apache.tika.sax.BodyContentHandler;
import org.springframework.stereotype.Service;
import org.xml.sax.SAXException;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

@Service
public class PdfParsingService {

    public Map<String, String> parsePdfsInDirectory(String directoryPath) throws IOException {
        Map<String, String> parsedPdfs = new HashMap<>();

        // 创建输出目录
        String outputDir = directoryPath + "/parsed_output";
        Files.createDirectories(Paths.get(outputDir));

        try (Stream<Path> paths = Files.walk(Paths.get(directoryPath))) {
            paths
                .filter(path -> path.toString().toLowerCase().endsWith(".pdf"))
                .forEach(pdfPath -> {
                    try {
                        File file = pdfPath.toFile();
                        
                        // 获取文件基本信息
                        long fileSize = file.length();
                        System.out.println("正在解析文件: " + file.getName() + " (大小: " + fileSize + " 字节)");
                        
                        // 先尝试普通解析
                        String content = parseWithTika(file);
                        int contentLength = content != null ? content.length() : 0;
                        System.out.println("普通解析提取的文本长度: " + contentLength + " 字符");
                        
                        // 如果内容很少且文件很大，尝试OCR解析
                        String ocrContent = null;
                        if (contentLength < 1000 && fileSize > 5000000) {
                            System.out.println("检测到可能的扫描版PDF，尝试OCR解析...");
                            ocrContent = parseWithOCR(file);
                            if (ocrContent != null && ocrContent.length() > contentLength) {
                                content = ocrContent;
                                contentLength = content.length();
                                System.out.println("OCR解析提取的文本长度: " + contentLength + " 字符");
                            }
                        }
                        
                        // 限制内容长度以避免过大的文件
                        if (contentLength > 100000) {
                            content = content.substring(0, 100000) + "\n\n[注：内容已截断，仅显示前100,000字符]";
                            contentLength = 100000;
                        }
                        
                        parsedPdfs.put(file.getName(), content);
                        
                        // 将解析内容保存为 TXT 文件
                        String txtFileName = file.getName().replace(".pdf", ".txt");
                        String txtFilePath = outputDir + "/" + txtFileName;
                        
                        try (FileWriter writer = new FileWriter(txtFilePath)) {
                            // 格式化输出，添加文件头信息
                            writer.write("=".repeat(80) + "\n");
                            writer.write("PDF 文件名: " + file.getName() + "\n");
                            writer.write("解析时间: " + new java.util.Date() + "\n");
                            writer.write("文件路径: " + file.getAbsolutePath() + "\n");
                            writer.write("文件大小: " + fileSize + " 字节\n");
                            writer.write("提取文本长度: " + contentLength + " 字符\n");
                            writer.write("解析方法: " + (ocrContent != null ? "OCR解析" : "普通解析") + "\n");
                            
                            // 检查是否可能是扫描版PDF
                            if (contentLength < 1000 && fileSize > 5000000) {
                                writer.write("警告: 文件较大但提取文本很少，可能是扫描版PDF或受保护的PDF\n");
                            }
                            
                            writer.write("=".repeat(80) + "\n\n");
                            
                            // 写入解析的内容，并进行一些基本的格式化
                            String formattedContent = formatContent(content);
                            writer.write(formattedContent);
                        }
                        
                        System.out.println("已保存解析结果到: " + txtFilePath);
                        
                    } catch (Exception e) {
                        System.err.println("解析文件 " + pdfPath.getFileName() + " 时出错: " + e.getMessage());
                        e.printStackTrace();
                    }
                });
        }

        return parsedPdfs;
    }
    
    private String parseWithTika(File file) {
        try {
            Tika tika = new Tika();
            return tika.parseToString(file);
        } catch (Exception e) {
            System.err.println("Tika解析失败: " + e.getMessage());
            return null;
        }
    }
    
    private String parseWithOCR(File file) {
        try {
            // 配置 Tika 解析器以启用 OCR
            TikaConfig config = TikaConfig.getDefaultConfig();
            Parser parser = new AutoDetectParser(config);
            
            // 配置 PDF 解析器以启用 OCR
            ParseContext parseContext = new ParseContext();
            parseContext.set(Parser.class, parser);
            
            // PDF 解析器配置
            PDFParserConfig pdfConfig = new PDFParserConfig();
            pdfConfig.setExtractInlineImages(true);
            pdfConfig.setExtractUniqueInlineImagesOnly(false);
            parseContext.set(PDFParserConfig.class, pdfConfig);
            
            // OCR 配置（如果系统安装了 Tesseract）
            TesseractOCRConfig ocrConfig = new TesseractOCRConfig();
            ocrConfig.setLanguage("eng+chi_sim"); // 英文和简体中文
            parseContext.set(TesseractOCRConfig.class, ocrConfig);
            
            Metadata metadata = new Metadata();
            BodyContentHandler handler = new BodyContentHandler(-1); // 不限制内容大小
            
            try (FileInputStream inputStream = new FileInputStream(file)) {
                parser.parse(inputStream, handler, metadata, parseContext);
                return handler.toString();
            }
            
        } catch (Exception e) {
            System.err.println("OCR解析失败: " + e.getMessage());
            return null;
        }
    }
    
    private String formatContent(String content) {
        if (content == null) return "";
        
        // 基本的文本格式化
        return content
                .replaceAll("\\s+", " ")  // 多个空白字符替换为单个空格
                .replaceAll("\\. ", ".\n")  // 句号后换行
                .replaceAll("\\? ", "?\n")  // 问号后换行
                .replaceAll("\\! ", "!\n")  // 感叹号后换行
                .replaceAll("\\n\\s*\\n", "\n\n")  // 多个连续换行替换为两个换行
                .trim();
    }
}
