package com.hujun.pdfparser;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;

/**
 * Markdown功能专用启动类
 * 排除数据库和JPA相关的自动配置，避免RAG系统初始化
 */
@SpringBootApplication(exclude = {
    DataSourceAutoConfiguration.class,
    HibernateJpaAutoConfiguration.class
}, scanBasePackages = {
    "com.hujun.pdfparser.markdown"
})
public class MarkdownApplication {

    public static void main(String[] args) {
        System.setProperty("spring.profiles.active", "markdown");
        SpringApplication.run(MarkdownApplication.class, args);
    }
}