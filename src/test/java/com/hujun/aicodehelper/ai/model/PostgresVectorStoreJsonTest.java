package com.hujun.aicodehelper.ai.model;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试PostgresVectorStore的JSON转义功能
 */
public class PostgresVectorStoreJsonTest {

    @Test
    public void testJsonEscaping() {
        // 创建PostgresVectorStore实例进行测试
        PostgresVectorStore store = new PostgresVectorStore(null, null);
        
        try {
            // 使用反射调用私有方法进行测试
            var escapeMethod = PostgresVectorStore.class.getDeclaredMethod("escapeJsonString", String.class);
            escapeMethod.setAccessible(true);
            
            // 测试基本字符串
            String result1 = (String) escapeMethod.invoke(store, "hello world");
            assertEquals("hello world", result1);
            
            // 测试包含引号的字符串
            String result2 = (String) escapeMethod.invoke(store, "hello \"world\"");
            assertEquals("hello \\\"world\\\"", result2);
            
            // 测试包含反斜杠的字符串
            String result3 = (String) escapeMethod.invoke(store, "hello\\world");
            assertEquals("hello\\\\world", result3);
            
            // 测试包含换行符的字符串
            String result4 = (String) escapeMethod.invoke(store, "hello\nworld");
            assertEquals("hello\\nworld", result4);
            
            // 测试null值
            String result5 = (String) escapeMethod.invoke(store, (String) null);
            assertEquals("", result5);
            
            // 测试复杂的JSON字符串（类似original_json的内容）
            String complexJson = "{\"id\":\"test\",\"content\":\"This is a \"test\" content\"}";
            String result6 = (String) escapeMethod.invoke(store, complexJson);
            String expected = "{\\\"id\\\":\\\"test\\\",\\\"content\\\":\\\"This is a \\\"test\\\" content\\\"}";
            assertEquals(expected, result6);
            
            System.out.println("✅ JSON转义测试通过");
            System.out.println("   - 基本字符串: " + result1);
            System.out.println("   - 引号转义: " + result2);
            System.out.println("   - 反斜杠转义: " + result3);
            System.out.println("   - 换行符转义: " + result4);
            System.out.println("   - 复杂JSON转义: " + result6);
            
        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testFormatMetadataAsJson() {
        PostgresVectorStore store = new PostgresVectorStore(null, null);
        
        try {
            // 使用反射调用私有方法进行测试
            var formatMethod = PostgresVectorStore.class.getDeclaredMethod("formatMetadataAsJson", java.util.Map.class);
            formatMethod.setAccessible(true);
            
            // 测试空Map
            java.util.Map<String, Object> emptyMap = new java.util.HashMap<>();
            String result1 = (String) formatMethod.invoke(store, emptyMap);
            assertEquals("{}", result1);
            
            // 测试包含字符串的Map
            java.util.Map<String, Object> stringMap = new java.util.HashMap<>();
            stringMap.put("file_name", "test.json");
            stringMap.put("math_type", "concept");
            String result2 = (String) formatMethod.invoke(store, stringMap);
            assertTrue(result2.contains("\"file_name\":\"test.json\""));
            assertTrue(result2.contains("\"math_type\":\"concept\""));
            
            // 测试包含特殊字符的Map
            java.util.Map<String, Object> specialMap = new java.util.HashMap<>();
            specialMap.put("content", "This is a \"test\" content with\nnewlines");
            specialMap.put("number", 123);
            specialMap.put("null_value", null);
            String result3 = (String) formatMethod.invoke(store, specialMap);
            assertTrue(result3.contains("\\\"test\\\""));
            assertTrue(result3.contains("\\n"));
            assertTrue(result3.contains("\"number\":123"));
            assertTrue(result3.contains("\"null_value\":null"));
            
            System.out.println("✅ 元数据格式化测试通过");
            System.out.println("   - 空Map: " + result1);
            System.out.println("   - 字符串Map: " + result2);
            System.out.println("   - 特殊字符Map: " + result3);
            
        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }
}
