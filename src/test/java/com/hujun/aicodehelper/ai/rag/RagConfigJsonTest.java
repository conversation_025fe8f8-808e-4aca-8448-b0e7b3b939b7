package com.hujun.aicodehelper.ai.rag;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import dev.langchain4j.data.document.Document;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试RagConfig的JSON数据加载功能
 */
@SpringBootTest
@ActiveProfiles("test")
public class RagConfigJsonTest {

    @Test
    public void testJsonDataStructure() throws IOException {
        // 测试data目录中的JSON文件结构
        Path dataPath = Path.of("data");
        
        if (!Files.exists(dataPath)) {
            System.out.println("⚠️ data目录不存在，跳过测试");
            return;
        }
        
        ObjectMapper objectMapper = new ObjectMapper();
        
        // 检查final-test.json
        Path finalTestPath = dataPath.resolve("final-test.json");
        if (Files.exists(finalTestPath)) {
            String jsonContent = Files.readString(finalTestPath);
            JsonNode rootNode = objectMapper.readTree(jsonContent);
            
            assertTrue(rootNode.isArray(), "final-test.json应该是一个数组");
            assertTrue(rootNode.size() > 0, "final-test.json应该包含至少一个项目");
            
            // 检查第一个项目的结构
            JsonNode firstItem = rootNode.get(0);
            assertNotNull(firstItem.get("id"), "应该有id字段");
            assertNotNull(firstItem.get("type"), "应该有type字段");
            assertNotNull(firstItem.get("content"), "应该有content字段");
            assertNotNull(firstItem.get("topic"), "应该有topic字段");
            assertNotNull(firstItem.get("grade"), "应该有grade字段");
            
            System.out.println("✅ final-test.json结构验证通过");
            System.out.println("   - 项目数量: " + rootNode.size());
            System.out.println("   - 第一个项目ID: " + firstItem.get("id").asText());
            System.out.println("   - 第一个项目类型: " + firstItem.get("type").asText());
        }
        
        // 检查volume-cube-detailed-guide.json
        Path volumePath = dataPath.resolve("volume-cube-detailed-guide.json");
        if (Files.exists(volumePath)) {
            String jsonContent = Files.readString(volumePath);
            JsonNode rootNode = objectMapper.readTree(jsonContent);
            
            assertTrue(rootNode.isArray(), "volume-cube-detailed-guide.json应该是一个数组");
            assertTrue(rootNode.size() > 0, "volume-cube-detailed-guide.json应该包含至少一个项目");
            
            System.out.println("✅ volume-cube-detailed-guide.json结构验证通过");
            System.out.println("   - 项目数量: " + rootNode.size());
        }
    }
    
    @Test
    public void testJsonItemProcessing(@TempDir Path tempDir) throws IOException {
        // 创建测试JSON文件
        String testJson = """
            [
                {
                    "id": "test_p5_concept_1",
                    "type": "concept",
                    "content": "This is a test concept for mathematics.",
                    "answer": null,
                    "latex": null,
                    "sympy_expr": null,
                    "graph": null,
                    "topic": "Algebra",
                    "grade": "P5",
                    "difficulty": "基础",
                    "source": "test",
                    "images": [],
                    "steps": [],
                    "metadata": {
                        "chapter": "Test Chapter",
                        "page": null,
                        "tags": ["test", "concept"]
                    }
                }
            ]
            """;
        
        Path testFile = tempDir.resolve("test.json");
        Files.writeString(testFile, testJson);
        
        // 创建RagConfig实例进行测试
        RagConfig ragConfig = new RagConfig();
        
        // 使用反射调用私有方法进行测试
        try {
            var method = RagConfig.class.getDeclaredMethod("processJsonItem", JsonNode.class, Path.class);
            method.setAccessible(true);
            
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(testJson);
            JsonNode testItem = rootNode.get(0);
            
            Document result = (Document) method.invoke(ragConfig, testItem, testFile);
            
            assertNotNull(result, "应该成功创建Document对象");
            assertNotNull(result.text(), "Document应该有文本内容");
            assertTrue(result.text().contains("test_p5_concept_1"), "文本应该包含ID");
            assertTrue(result.text().contains("This is a test concept"), "文本应该包含内容");
            assertTrue(result.text().contains("Algebra"), "文本应该包含主题");
            assertTrue(result.text().contains("P5"), "文本应该包含年级");
            
            // 检查元数据
            assertEquals("test.json", result.metadata().getString("file_name"));
            assertEquals("JSON", result.metadata().getString("file_type"));
            assertEquals("test_p5_concept_1", result.metadata().getString("math_id"));
            assertEquals("concept", result.metadata().getString("math_type"));
            assertEquals("Algebra", result.metadata().getString("math_topic"));
            assertEquals("P5", result.metadata().getString("math_grade"));
            
            System.out.println("✅ JSON项目处理测试通过");
            System.out.println("   - 文档长度: " + result.text().length());
            System.out.println("   - 元数据字段数: " + result.metadata().toMap().size());
            
        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }
}
