package com.hujun.aicodehelper;

import com.hujun.aicodehelper.ai.model.MathRagKnowledgeStore;
import com.hujun.aicodehelper.ai.model.LocalEmbeddingModel;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.sql.DataSource;

@SpringBootTest
@ActiveProfiles("postgresql")
public class MathRagKnowledgeTest {

    @Autowired
    private DataSource dataSource;

    @Test
    public void testMathRagKnowledgeStore() {
        System.out.println("=== 测试数学知识库存储 ===");

        // 创建本地嵌入模型（使用与配置相同的参数）
        LocalEmbeddingModel embeddingModel = new LocalEmbeddingModel(
            "http://8.137.78.144:9000",
            "BAAI/bge-base-en-v1.5",
            768
        );

        // 创建数学知识库存储
        MathRagKnowledgeStore mathStore = new MathRagKnowledgeStore(dataSource, embeddingModel);

        // 检查现有数据
        long existingCount = mathStore.getVectorCount();
        System.out.println("📊 math_rag_knowledge表现有记录数: " + existingCount);

        // 添加测试数据
        if (existingCount == 0) {
            System.out.println("🚀 开始添加测试数学知识数据...");

            String testId = "test_math_concept_001";
            String testContent = "勾股定理是直角三角形中两个直角边的平方和等于斜边的平方。";
            String testAnswer = "a² + b² = c²";
            String testLatex = "a^{2} + b^{2} = c^{2}";
            String testTopic = "Geometry";
            String testGrade = "P6";
            String testType = "concept";
            String testDifficulty = "基础";
            String testSource = "测试数据";

            try {
                mathStore.addMathKnowledge(
                    testId, testType, testContent, testTopic, testGrade,
                    testAnswer, testLatex, null, null, testDifficulty, testSource,
                    "[]", "[]", "{}"
                );

                System.out.println("✅ 测试数据添加成功!");

                // 再次检查记录数
                long newCount = mathStore.getVectorCount();
                System.out.println("📊 添加后记录数: " + newCount);

            } catch (Exception e) {
                System.err.println("❌ 添加测试数据失败: " + e.getMessage());
                e.printStackTrace();
            }
        } else {
            System.out.println("✅ 数学知识库表已有数据，无需添加测试数据");
        }
    }
}
