# 数据库迁移说明

## 概述

本次更新将项目从双数据库架构（MySQL + PostgreSQL）迁移到单一PostgreSQL数据库架构。这样做的目的是简化数据源管理，减少系统复杂性，并统一所有数据存储在PostgreSQL中。

## 变更内容

### 1. 数据库架构变更

**变更前：**
- MySQL：存储聊天记忆（chat_messages表）
- PostgreSQL：存储向量数据（embeddings表）和RAG功能

**变更后：**
- PostgreSQL：统一存储聊天记忆和向量数据

### 2. 代码变更

1. **配置文件更新**
   - 移除了MySQL数据源配置
   - 将聊天消息相关的Repository配置指向PostgreSQL数据源
   - 更新了数据库连接信息

2. **Java代码变更**
   - 重命名`MySqlChatMemoryStore`为`PostgreSqlChatMemoryStore`
   - 更新了相关的注释和文档字符串
   - 移除了MultiDataSourceConfig中的MySQL配置
   - 更新了ChatMemoryConfig.java中的引用

3. **依赖变更**
   - 从pom.xml中移除了MySQL驱动依赖
   - 保留了PostgreSQL驱动依赖

4. **脚本更新**
   - 更新了数据库检查脚本，适配单一PostgreSQL配置
   - 创建了PostgreSQL版本的chat_messages表初始化脚本

### 3. 数据库表变更

1. **chat_messages表**
   - 在PostgreSQL中创建了与MySQL结构相同的chat_messages表
   - 保留了原有的索引和触发器
   - 添加了示例数据

2. **embeddings表**
   - 保持原有的表结构和功能不变

## 部署说明

### 1. 数据库准备

1. 确保PostgreSQL数据库服务正在运行
2. 确保已安装pgvector扩展
3. 执行PostgreSQL初始化脚本：
   ```bash
   psql -h your_host -p your_port -U your_user -d your_database -f src/main/resources/db/postgresql_chat_messages.sql
   ```

### 2. 配置更新

1. 检查`src/main/resources/application-postgresql.yml`中的数据库连接配置
2. 确保用户名、密码和连接URL正确

### 3. 应用启动

1. 构建项目：
   ```bash
   ./mvnw clean package
   ```

2. 启动应用：
   ```bash
   ./mvnw spring-boot:run
   ```

## 测试验证

### 1. 功能测试

1. 验证聊天功能是否正常工作
2. 验证聊天记录是否正确存储到PostgreSQL
3. 确认RAG功能不受影响

### 2. 数据验证

1. 检查PostgreSQL中的chat_messages表是否正常创建
2. 验证数据是否能正确插入和查询
3. 确认原有的embeddings表功能正常

## 注意事项

1. **数据迁移**：如果之前有MySQL中的聊天记录数据，需要手动迁移至PostgreSQL
2. **备份**：在部署前请备份现有数据库
3. **回滚**：如果遇到问题，可以使用之前的版本进行回滚

## 问题排查

如果遇到问题，请检查以下几点：

1. 数据库连接配置是否正确
2. PostgreSQL服务是否正常运行
3. pgvector扩展是否已安装
4. 应用启动日志中是否有数据源初始化错误

## 编译错误修复

如果您在迁移后遇到编译错误，请检查以下文件是否已正确更新：

1. `ChatMemoryConfig.java` - 确保已将`MySqlChatMemoryStore`引用更新为`PostgreSqlChatMemoryStore`
2. `pom.xml` - 确保已移除MySQL依赖
3. 其他配置文件 - 确保所有MySQL相关的引用都已更新为PostgreSQL