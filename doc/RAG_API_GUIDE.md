# RAG 交互控制器 API 指南

本文档介绍了新增的 RAG（Retrieval Augmented Generation）交互控制器的使用方法和API接口。

## 概述

RAG 交互控制器为 AI 编程小助手项目增加了基于知识库的智能问答功能，能够：

- 基于本地知识库进行增强问答
- 直接搜索知识库内容
- 提供流式和同步两种交互方式
- 支持知识库统计信息查询

## 知识库内容

当前知识库包含以下文档：
- `Java 编程学习路线.md` - 完整的Java学习路径指导
- `程序员常见面试题.md` - 高频面试题汇总
- `鱼皮的求职指南.md` - 求职全流程指导
- `鱼皮的项目学习建议.md` - 项目学习规划建议

## API 接口

### 1. RAG 增强问答（同步）

**接口**: `POST /api/ai/rag/chat`

**功能**: 基于知识库进行问答，返回答案和相关文档来源

**请求体**:
```json
{
  "message": "Java学习需要多长时间？",
  "context": "我是编程新手"  // 可选
}
```

**响应**:
```json
{
  "success": true,
  "answer": "根据个人基础和学习时间，Java学习通常需要...",
  "sources": [
    {
      "text": "相关知识库内容片段",
      "fileName": "Java 编程学习路线.md",
      "metadata": {}
    }
  ]
}
```

### 2. RAG 增强问答（流式）

**接口**: `GET /api/ai/rag/chat-stream`

**功能**: 基于知识库进行问答，实时返回流式响应

**参数**:
- `memoryId`: 会话ID（数字）
- `message`: 用户消息（字符串）

**响应**: SSE流式数据

### 3. 知识库搜索

**接口**: `POST /api/ai/rag/search`

**功能**: 直接搜索知识库中的相关内容，不进行对话生成

**请求体**:
```json
{
  "query": "Redis面试题",
  "maxResults": 5,     // 最大返回结果数，默认5
  "minScore": 0.7      // 最小相似度分数，默认0.7
}
```

**响应**:
```json
{
  "success": true,
  "query": "Redis面试题",
  "total": 3,
  "results": [
    {
      "text": "Redis的数据类型有哪些？分别有什么应用场景？",
      "score": 0.85,
      "fileName": "程序员常见面试题.md",
      "metadata": {}
    }
  ]
}
```

### 4. 知识库统计信息

**接口**: `GET /api/ai/rag/stats`

**功能**: 获取知识库的基本统计信息

**响应**:
```json
{
  "storeType": "内存向量数据库",
  "description": "包含Java学习路线、面试题、求职指南、项目建议等知识文档",
  "documentCount": 4,
  "embeddingModel": "text-embedding-004"
}
```

## 使用场景

### 1. 学习路线规划
```bash
curl -X POST http://localhost:8081/api/ai/rag/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "我想学习Java，应该从哪里开始？"}'
```

### 2. 面试准备
```bash
curl -X POST http://localhost:8081/api/ai/rag/search \
  -H "Content-Type: application/json" \
  -d '{"query": "Spring Boot面试题", "maxResults": 3}'
```

### 3. 求职指导
```bash
curl -X POST http://localhost:8081/api/ai/rag/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "如何写一份好的程序员简历？"}'
```

### 4. 项目学习建议
```bash
curl -X POST http://localhost:8081/api/ai/rag/search \
  -H "Content-Type: application/json" \
  -d '{"query": "推荐的项目学习顺序"}'
```

## 技术实现

### 核心组件

1. **RagService**: RAG专用服务类
   - 知识库搜索
   - RAG增强问答
   - 统计信息获取

2. **AiController**: 增强的控制器
   - 新增RAG相关接口
   - 支持同步和流式响应

3. **ContentRetriever**: 内容检索器
   - 基于向量相似度搜索
   - 文档片段检索

### 数据流程

1. **文档加载**: 从`src/main/resources/docs`加载Markdown文档
2. **文档切割**: 按段落切割，最大1000字符，重叠200字符
3. **向量化**: 使用Google AI的text-embedding-004模型
4. **存储**: 存储在内存向量数据库中
5. **检索**: 根据查询向量检索相关文档片段
6. **生成**: 结合检索到的内容生成答案

### 配置信息

RAG功能的配置在以下文件中：
- `RagConfig.java`: RAG检索配置
- `EmbeddingConfig.java`: 嵌入模型配置
- `application.yml`: API密钥配置

## 测试

运行测试来验证RAG功能：

```bash
./mvnw test -Dtest=RagServiceTest
```

测试用例包括：
- 知识库搜索测试
- RAG问答测试
- 统计信息测试
- 特定主题搜索测试

## 前端集成

可以在前端中调用这些API来构建RAG功能界面：

```javascript
// 知识库搜索
async function searchKnowledge(query) {
  const response = await fetch('/api/ai/rag/search', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ query, maxResults: 5 })
  });
  return response.json();
}

// RAG问答
async function ragChat(message) {
  const response = await fetch('/api/ai/rag/chat', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ message })
  });
  return response.json();
}
```

## 扩展建议

1. **多数据源支持**: 支持数据库、API等多种知识来源
2. **向量数据库**: 集成专业向量数据库如Pinecone、Weaviate
3. **文档更新**: 支持动态添加、更新、删除知识库文档
4. **权限控制**: 添加知识库访问权限管理
5. **缓存优化**: 添加查询结果缓存提升性能
6. **多语言支持**: 支持多语言知识库和查询

## 注意事项

1. 确保Google AI API密钥配置正确
2. 知识库文档需要放在`src/main/resources/docs`目录
3. 向量化过程在应用启动时执行，大量文档可能影响启动时间
4. 内存向量数据库重启后数据会丢失，生产环境建议使用持久化方案 