# text-embedding-004 向量化日志查看指南

我已经为项目添加了详细的向量化过程日志，让你可以清楚地看到 Google AI 的 text-embedding-004 模型的工作过程。

## 🔍 日志内容

添加的日志包括：

### 1. 应用启动时的 RAG 初始化日志
- 嵌入模型配置信息
- 知识库文档加载过程
- 文档切割和向量化过程
- 向量数据库初始化
- 整体耗时统计

### 2. 实时查询时的向量化日志
- 查询文本向量化过程
- 检索耗时统计
- 检索结果详情
- 知识源匹配情况

### 3. 详细的向量化测试日志
- 单文本向量化
- 批量文本向量化
- 长文本处理
- 向量维度和相似度分析

## 🚀 查看日志的方法

### 方法一：启动应用查看初始化日志

1. 启动后端应用：
```bash
cd /Users/<USER>/Desktop/RAG/ai-code-helper-master
./mvnw spring-boot:run
```

2. 观察控制台输出，你会看到类似这样的日志：

```
🤖 正在初始化 Google AI 嵌入模型...
📋 模型配置信息:
   - 模型名称: text-embedding-004
   - API Key: AIzaSyBA79...D8

💾 正在初始化内存向量数据库...
✅ 内存向量数据库初始化完成

=== 开始初始化 RAG 知识库 ===
📁 正在加载知识库文档...
✅ 成功加载 4 个文档
📄 文档 1: Java 编程学习路线.md (长度: 108567 字符)
📄 文档 2: 程序员常见面试题.md (长度: 3456 字符)
📄 文档 3: 鱼皮的求职指南.md (长度: 56789 字符)
📄 文档 4: 鱼皮的项目学习建议.md (长度: 14567 字符)

✂️ 正在切割文档，最大片段长度: 1000字符，重叠: 200字符
🔧 正在构建向量存储摄取器...
🚀 开始向量化处理，使用模型: text-embedding-004
✅ 向量化完成！耗时: 2356 毫秒

🎉 RAG 知识库初始化完成！
=== RAG 配置信息 ===
📚 知识库文档数量: 4
🔍 最大检索结果: 5
📊 最小相似度分数: 0.75
🤖 嵌入模型: text-embedding-004
========================
```

### 方法二：运行专门的向量化测试

运行嵌入模型测试来查看详细的向量化过程：

```bash
./mvnw test -Dtest=EmbeddingModelTest
```

你会看到详细的测试日志：

```
🧪 开始测试单文本向量化
📝 测试文本: 'Java是一种面向对象的编程语言'
📏 文本长度: 15 字符
🚀 调用 text-embedding-004 模型进行向量化...
✅ 向量化完成！耗时: 234 毫秒
📊 向量信息:
   - 向量维度: 768
   - 前10个值: [-0.1234, 0.5678, -0.2345, ...]
   - 向量范数: 0.987654
🎫 Token 使用情况:
   - 输入tokens: 8
   - 总tokens: 8
```

### 方法三：通过API调用查看实时日志

调用RAG相关的API接口，观察实时的向量化日志：

```bash
# 测试RAG问答
curl -X POST http://localhost:8081/api/ai/rag/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Java学习需要多长时间？"}'

# 测试知识库搜索
curl -X POST http://localhost:8081/api/ai/rag/search \
  -H "Content-Type: application/json" \
  -d '{"query": "Redis面试题", "maxResults": 3}'
```

调用时会看到日志：

```
💬 开始 RAG 增强问答 - 问题: 'Java学习需要多长时间？'
🚀 正在调用 text-embedding-004 模型进行问题向量化和知识库检索...
✅ RAG 问答完成！总耗时: 1234 毫秒
📝 生成答案长度: 456 字符
📚 使用知识源数量: 3
📄 知识源 1: Java 编程学习路线.md
📄 知识源 2: 鱼皮的项目学习建议.md
📄 知识源 3: 程序员常见面试题.md
🎉 RAG 问答结果构建完成
```

### 方法四：运行RAG服务测试

```bash
./mvnw test -Dtest=RagServiceTest
```

观察RAG服务的详细工作过程。

## 📝 日志级别配置

如果想看到更详细的DEBUG级别日志，可以在 `application.yml` 中添加：

```yaml
logging:
  level:
    com.hujun.aicodehelper.ai: DEBUG
    dev.langchain4j: DEBUG
```

## 🎯 重点观察项

通过这些日志，你可以观察到：

1. **模型初始化过程**：API密钥配置、模型名称确认
2. **文档处理过程**：文档加载、切割、转换
3. **向量化性能**：处理时间、文档数量、片段数量
4. **检索过程**：查询向量化、相似度匹配、结果筛选
5. **Token使用情况**：API调用的Token消耗
6. **向量特征**：维度、范数、相似度等技术参数

## 🔧 自定义日志

你也可以在代码中添加更多自定义日志来观察特定的向量化细节，所有相关的服务类都已经添加了 `@Slf4j` 注解，可以直接使用 `log.info()` 等方法。

这样你就可以完整地观察到 text-embedding-004 模型的向量化过程了！ 