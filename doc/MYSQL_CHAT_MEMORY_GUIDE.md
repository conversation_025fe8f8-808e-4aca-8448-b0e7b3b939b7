# MySQL 聊天记忆使用指南

本文档介绍如何使用基于 MySQL 的持久化聊天记忆功能。

## 📋 功能特性

- ✅ **持久化存储**：聊天记录保存到 MySQL 数据库
- ✅ **多会话支持**：支持多个独立的聊天会话
- ✅ **用户隔离**：每个用户有独立的聊天记忆
- ✅ **窗口管理**：支持消息窗口和令牌窗口两种模式
- ✅ **Spring Boot 集成**：完整的 Repository + Bean 架构
- ✅ **LangChain4j 兼容**：遵循 LangChain4j ChatMemory 标准

## 🛠️ 快速开始

### 1. 数据库初始化

执行 SQL 脚本初始化数据库表：

```bash
mysql -u your_username -p your_database < src/main/resources/db/mysql_init.sql
```

### 2. 配置应用

使用 MySQL 配置启动应用：

```bash
# 方式1：使用配置文件
./mvnw spring-boot:run -Dspring.profiles.active=mysql

# 方式2：运行示例
./mvnw spring-boot:run -Dspring.profiles.active=mysql,demo
```

### 3. 配置参数

在 `application-mysql.yml` 中配置聊天记忆参数：

```yaml
chat:
  memory:
    type: message          # 窗口类型: message 或 token
    max-messages: 20       # 最大消息数量
    max-tokens: 1000       # 最大令牌数量
```

## 💻 代码使用示例

### 基础使用

```java
@Autowired
private ChatMemoryService chatMemoryService;

// 添加消息到会话
String sessionId = "user_session_123";
UserMessage userMsg = UserMessage.from("你好，请介绍Java特点");
chatMemoryService.addMessage(sessionId, userMsg);

AiMessage aiMsg = AiMessage.from("Java是一种面向对象的编程语言...");
chatMemoryService.addMessage(sessionId, aiMsg);

// 获取会话历史
List<ChatMessage> messages = chatMemoryService.getMessages(sessionId);
```

### 用户专属记忆

```java
// 为特定用户添加消息
String userId = "user_456";
chatMemoryService.addUserMessage(userId, userMsg);

// 获取用户的所有消息
List<ChatMessage> userMessages = chatMemoryService.getUserMessages(userId);
```

### 与 AI Services 集成

```java
@Configuration
public class AiServiceConfig {
    
    @Autowired
    private ChatMemoryProvider chatMemoryProvider;
    
    @Bean
    public MyAiService myAiService() {
        return AiServices.builder(MyAiService.class)
                .chatLanguageModel(chatModel)
                .chatMemoryProvider(chatMemoryProvider)  // 使用持久化记忆
                .build();
    }
}

// AI Service 接口
public interface MyAiService {
    String chat(@MemoryId String sessionId, @UserMessage String message);
}
```

## 🗄️ 数据库结构

### chat_messages 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| memory_id | VARCHAR(255) | 内存ID，区分不同会话 |
| message_order | INT | 消息顺序 |
| message_type | VARCHAR(50) | 消息类型 |
| message_content | TEXT | 消息内容（JSON格式） |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

### 索引

- `idx_memory_id`: 按内存ID查询
- `idx_memory_id_order`: 按内存ID和顺序查询

## 🔧 核心组件

### 1. ChatMessageEntity
JPA 实体类，映射数据库表结构。

### 2. ChatMessageRepository  
Spring Data JPA Repository，提供数据访问方法。

### 3. MySqlChatMemoryStore
实现 LangChain4j ChatMemoryStore 接口，处理消息的序列化和存储。

### 4. ChatMemoryConfig
配置类，创建 ChatMemoryProvider Bean。

### 5. ChatMemoryService
业务服务类，提供便捷的聊天记忆管理方法。

## 📊 配置选项

### 记忆窗口类型

#### 消息窗口（Message Window）
```yaml
chat:
  memory:
    type: message
    max-messages: 20  # 保留最近20条消息
```

#### 令牌窗口（Token Window）
```yaml
chat:
  memory:
    type: token
    max-tokens: 1000  # 保留最近1000个令牌的消息
```

## 🚀 高级功能

### 1. 记忆管理

```java
// 检查是否有历史记录
boolean hasHistory = chatMemoryService.hasMessages(sessionId);

// 获取消息数量
long messageCount = chatMemoryService.getMessageCount(sessionId);

// 清空聊天记忆
chatMemoryService.clearChatMemory(sessionId);
```

### 2. 批量操作

```java
// 直接访问 Repository 进行批量操作
@Autowired
private ChatMessageRepository repository;

// 删除特定用户的所有记录
repository.deleteByMemoryId("user_123");

// 查询特定时间范围的消息
// 可以扩展 Repository 添加自定义查询方法
```

## 🔒 注意事项

### 性能优化

1. **索引使用**：确保 memory_id 上有索引
2. **消息长度**：避免存储过长的消息内容
3. **定期清理**：实现老旧会话的清理机制

### 安全考虑

1. **数据加密**：敏感消息内容可考虑加密存储
2. **访问控制**：确保用户只能访问自己的聊天记录
3. **数据备份**：定期备份聊天记录数据

### 扩展性

1. **分表策略**：大量数据时可按时间或用户分表
2. **缓存集成**：可集成 Redis 缓存热点数据
3. **异步处理**：消息存储可使用异步方式提高性能

## 📚 相关链接

- [LangChain4j Chat Memory 文档](https://docs.langchain4j.dev/tutorials/chat-memory/)
- [Spring Data JPA 文档](https://spring.io/projects/spring-data-jpa)
- [MySQL 官方文档](https://dev.mysql.com/doc/)

## 🎯 示例项目

运行示例查看完整功能演示：

```bash
./mvnw spring-boot:run -Dspring.profiles.active=mysql,demo
```

示例将演示：
- 基础聊天记忆使用
- 用户专属记忆管理  
- 记忆查询和清理操作