# 语音合成功能使用指南

## 功能概述

我们已经成功为AI代码助手添加了完整的语音合成功能，让用户可以将AI生成的文章内容转换为高质量语音文件。

## 已完成的功能

### 🎯 后端API接口

1. **基础语音合成**
   - `POST /api/ai/speech/synthesize`
   - 将文本转换为语音文件（使用默认中文女声）

2. **自定义语音合成**
   - `POST /api/ai/speech/synthesize-with-voice`
   - 使用指定的语音进行合成

3. **语音列表**
   - `GET /api/ai/speech/voices`
   - 获取支持的所有语音选项

4. **文件管理**
   - `GET /api/ai/speech/download` - 下载语音文件
   - `GET /api/ai/speech/files` - 获取语音文件列表

### 🎨 前端组件

1. **SpeechPlayer.vue** - 语音播放组件
   - 支持语音合成、播放控制、进度显示
   - 支持下载语音文件
   - 可配置语音类型

2. **SpeechSettings.vue** - 语音设置组件
   - 语音类型选择
   - 自动播放设置
   - 语音质量控制
   - 文件管理
   - 语音测试功能

3. **集成到聊天界面**
   - 每条AI回复都有语音合成按钮
   - 支持RAG和普通聊天模式
   - 统一的语音控制

### 📡 API调用模块

**speechApi.js** - 完整的语音API封装
- 语音合成功能
- 文件下载管理
- 音频播放控制
- 错误处理

## 使用方法

### 基本使用

1. **发送消息给AI**
   - 在聊天界面输入问题
   - AI回复后，消息下方会出现"🎤 生成语音"按钮

2. **生成语音**
   - 点击"生成语音"按钮
   - 系统自动调用Azure语音服务
   - 生成完成后可直接播放

3. **语音控制**
   - ▶️ 播放/暂停
   - ⏹️ 停止播放  
   - 💾 下载文件

### 高级设置

1. **打开语音设置**
   - 点击页面右上角"🎤 语音设置"按钮

2. **配置选项**
   - 选择默认语音（中文女声/男声，英文女声/男声）
   - 启用/关闭自动播放
   - 调整语音质量
   - 设置播放速度

3. **文件管理**
   - 查看所有生成的语音文件
   - 播放历史文件
   - 下载文件到本地

## 支持的语音

| 语音代码 | 语言 | 性别 | 描述 |
|---------|------|------|------|
| zh-CN-XiaoxiaoNeural | 中文 | 女 | 晓晓（温柔女声）|
| zh-CN-YunxiNeural | 中文 | 男 | 云希（成熟男声）|
| zh-CN-YunyangNeural | 中文 | 男 | 云扬（青年男声）|
| en-US-AriaNeural | 英文 | 女 | Aria（清晰女声）|
| en-US-JennyNeural | 英文 | 女 | Jenny（友好女声）|
| en-US-GuyNeural | 英文 | 男 | Guy（专业男声）|

## 技术架构

### 后端（Java Spring Boot）
```
AiController.java
├── 语音合成接口
├── 文件下载接口
├── 语音管理接口
└── 错误处理

SpeechSynthesisService.java
├── Azure语音服务集成
├── 文件生成管理
├── 语音参数配置
└── 资源清理
```

### 前端（Vue.js）
```
speechApi.js (API层)
├── 语音合成调用
├── 文件下载处理
├── 音频播放控制
└── 错误处理

SpeechPlayer.vue (播放组件)
├── 语音合成界面
├── 播放控制器
├── 进度显示
└── 设置集成

SpeechSettings.vue (设置组件)
├── 语音配置
├── 文件管理
├── 测试功能
└── 偏好保存
```

## 配置要求

### Azure语音服务

在 `application-postgresql.yml` 中配置：
```yaml
azure:
  speech:
    key: "your-azure-speech-key"
    region: "eastus"
    voice: "zh-CN-XiaoxiaoNeural"

speech:
  output:
    directory: "./data/speech-output"
```

### 前端依赖

确保已安装必要的依赖：
```bash
cd ai-code-helper-frontend
npm install
```

## 启动测试

### 1. 启动后端
```bash
cd /Users/<USER>/Desktop/RAG/ai-code-helper-master
./mvnw spring-boot:run
```

### 2. 启动前端
```bash
cd ai-code-helper-frontend
npm run dev
```

### 3. 测试流程
1. 打开浏览器访问 `http://localhost:5173`
2. 发送消息给AI
3. 等待AI回复
4. 点击语音合成按钮测试功能
5. 在语音设置中测试不同配置

## 常见问题

### Q: 语音合成失败
**A:** 检查Azure语音服务配置和网络连接

### Q: 音频无法播放
**A:** 确认浏览器支持音频播放，检查文件是否生成

### Q: 下载失败
**A:** 检查服务器文件权限和存储空间

### Q: 语音质量不佳
**A:** 尝试不同的语音选项，或调整语音质量设置

## 扩展功能

未来可考虑添加的功能：
- 语音参数调节（语速、音调、音量）
- 批量文章语音合成
- 语音文件格式选择（MP3、OGG等）
- 语音合成进度跟踪
- 云端语音文件存储

## 结论

语音合成功能已完全集成到AI代码助手中，为用户提供了丰富的多媒体交互体验。该功能支持多种语音选择，具有完整的播放控制和文件管理能力，可以大大提升用户的使用体验。