# 单一数据库架构配置指南

## 🎯 架构概述

本项目现在采用**单一数据库架构**，统一使用PostgreSQL来存储所有数据：

| 数据库 | 用途 | 技术栈 | 数据类型 |
|--------|------|--------|----------|
| **PostgreSQL** | 统一数据存储 | JPA + Hibernate + pgvector | 关系型数据 + 向量嵌入 |

### 🏗️ 架构优势

1. **简化管理**：只需要维护一个数据库实例
2. **降低成本**：减少数据库许可证和维护成本
3. **数据一致性**：所有数据都在同一个数据库中，便于管理
4. **性能优化**：可以针对单一数据库进行优化

## 📁 配置文件结构

```
src/main/resources/
├── application.yml                 # 主配置文件
├── application-postgresql.yml      # PostgreSQL配置
└── db/
    ├── postgresql_chat_messages.sql # 聊天消息表初始化脚本
    ├── init_postgresml.sql         # 向量表初始化脚本
    └── scripts/
        └── check-database-setup.sh # 数据库检查脚本
```

## ⚙️ PostgreSQL配置

### 数据源配置

```yaml
spring:
  datasource:
    postgresql:
      url: *******************************************
      username: myappuser
      password: mypassword
      driver-class-name: org.postgresql.Driver
      hikari:
        pool-name: PostgreSQLPool
        maximum-pool-size: 15
        minimum-idle: 3
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
```

### JPA配置

```yaml
spring:
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
```

## 🗄️ 数据库表结构

### chat_messages表

用于存储聊天记忆：

```sql
CREATE TABLE IF NOT EXISTS chat_messages (
    id BIGSERIAL PRIMARY KEY,
    memory_id VARCHAR(255) NOT NULL,
    message_order INTEGER NOT NULL,
    message_type VARCHAR(50) NOT NULL,
    message_content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### embeddings表

用于存储向量数据：

```sql
CREATE TABLE IF NOT EXISTS embeddings (
    id SERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB,
    embedding vector(1024),
    file_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🚀 初始化步骤

### 1. 安装PostgreSQL和pgvector

```bash
# 安装PostgreSQL 15+
# 安装pgvector扩展
CREATE EXTENSION IF NOT EXISTS vector;
```

### 2. 创建数据库和用户

```sql
CREATE DATABASE postgresml;
CREATE USER myappuser WITH PASSWORD 'mypassword';
GRANT ALL PRIVILEGES ON DATABASE postgresml TO myappuser;
```

### 3. 执行初始化脚本

```bash
# 初始化聊天消息表
psql -h localhost -p 5433 -U myappuser -d postgresml -f src/main/resources/db/postgresql_chat_messages.sql

# 初始化向量表
psql -h localhost -p 5433 -U myappuser -d postgresml -f src/main/resources/db/init_postgresml.sql
```

## 🔧 配置验证

### 检查脚本

使用提供的检查脚本来验证配置：

```bash
chmod +x src/main/resources/db/scripts/check-database-setup.sh
./src/main/resources/db/scripts/check-database-setup.sh
```

### 手动验证

```bash
# 检查数据库连接
psql -h localhost -p 5433 -U myappuser -d postgresml -c "SELECT 1;"

# 检查表是否存在
psql -h localhost -p 5433 -U myappuser -d postgresml -c "\d chat_messages"
psql -h localhost -p 5433 -U myappuser -d postgresml -c "\d embeddings"
```

## 📊 性能优化

### 连接池配置

```yaml
hikari:
  pool-name: PostgreSQLPool
  maximum-pool-size: 15
  minimum-idle: 3
  connection-timeout: 30000
  idle-timeout: 600000
  max-lifetime: 1800000
```

### 索引优化

```sql
-- 为聊天消息表创建索引
CREATE INDEX IF NOT EXISTS idx_memory_id ON chat_messages(memory_id);
CREATE INDEX IF NOT EXISTS idx_memory_id_order ON chat_messages(memory_id, message_order);

-- 为向量表创建索引
CREATE INDEX IF NOT EXISTS embeddings_embedding_idx 
ON embeddings USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100);
```

## 🛡️ 安全配置

### 用户权限

```sql
-- 创建专用用户
CREATE USER myappuser WITH PASSWORD 'strong_password';

-- 授予必要权限
GRANT CONNECT ON DATABASE postgresml TO myappuser;
GRANT USAGE ON SCHEMA public TO myappuser;
GRANT ALL PRIVILEGES ON TABLE chat_messages TO myappuser;
GRANT ALL PRIVILEGES ON TABLE embeddings TO myappuser;
GRANT ALL PRIVILEGES ON SEQUENCE chat_messages_id_seq TO myappuser;
GRANT ALL PRIVILEGES ON SEQUENCE embeddings_id_seq TO myappuser;
```

## 📈 监控和维护

### 日志配置

```yaml
logging:
  level:
    com.hujun.aicodehelper.chat: DEBUG
    com.hujun.aicodehelper.ai: DEBUG
    org.hibernate.SQL: DEBUG
    org.springframework.transaction: DEBUG
```

### 定期维护

```sql
-- 分析表统计信息
ANALYZE chat_messages;
ANALYZE embeddings;

-- 清理过期数据（根据需要）
DELETE FROM chat_messages WHERE created_at < NOW() - INTERVAL '30 days';
```

## 🆘 故障排除

### 常见问题

1. **连接失败**：
   - 检查数据库服务是否运行
   - 验证连接参数是否正确
   - 确认防火墙设置

2. **权限错误**：
   - 检查用户权限配置
   - 确认数据库和表的访问权限

3. **性能问题**：
   - 检查索引是否正确创建
   - 分析查询执行计划
   - 调整连接池配置

### 日志分析

查看应用日志中的数据库相关条目：

```bash
# 查看聊天记忆相关的日志
grep "chat.memory" logs/application.log

# 查看向量处理相关的日志
grep "embedding" logs/application.log
```

## 🔄 迁移指南

如果您是从双数据库架构迁移到单一数据库架构，请参考DB_MIGRATION_README.md文件。