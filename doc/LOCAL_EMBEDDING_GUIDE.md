# 本地嵌入模型集成指南

本项目现在支持使用本地部署的 BGE-large-en-v1.5 向量模型，作为 Google AI text-embedding-004 的替代方案。

## 🚀 功能特性

- ✅ 支持本地 BGE-large-en-v1.5 模型 (1024维向量)
- ✅ 支持 Google AI text-embedding-004 模型 (768维向量)  
- ✅ 可配置模型选择
- ✅ 动态向量维度支持
- ✅ 兼容现有 RAG 系统
- ✅ PostgreSQL 向量数据库适配

## 📋 配置说明

### 使用本地模型 (bge-large-en-v1.5)

在 `application.yml` 中设置：

```yaml
embedding:
  # 选择本地模型
  model-type: local
  local:
    base-url: http://localhost:8000
    model-name: bge-large-en-v1.5
    dimension: 1024
```

### 使用 Google AI 模型 (text-embedding-004)

在 `application.yml` 中设置：

```yaml
embedding:
  # 选择 Google AI 模型
  model-type: google_ai
  google-ai:
    model-name: text-embedding-004
    dimension: 768
```

## 🛠️ 本地模型服务器部署

### 1. 安装 Python 依赖

```bash
pip install -r requirements.txt
```

### 2. 启动本地服务器

```bash
python local_embedding_server.py
```

服务器将在 `http://localhost:8000` 启动，提供以下端点：

- `POST /v1/embeddings` - 创建文本嵌入向量
- `GET /health` - 健康检查
- `GET /models` - 列出可用模型

### 3. 测试服务器

```bash
python test_local_embedding.py
```

## 📊 性能对比

| 模型 | 维度 | 优势 | 劣势 |
|------|------|------|------|
| BGE-large-en-v1.5 | 1024 | 本地部署、隐私保护、无API费用 | 需要本地资源、初次加载慢 |
| text-embedding-004 | 768 | 响应快速、无需本地资源 | 需要API密钥、网络依赖 |

## 🔧 API 格式

### 请求格式

```json
{
    "input": "Java是一种面向对象的编程语言",
    "model": "bge-large-en-v1.5"
}
```

### 响应格式

```json
{
    "object": "list",
    "data": [
        {
            "object": "embedding",
            "index": 0,
            "embedding": [0.1, 0.2, ...]
        }
    ],
    "model": "bge-large-en-v1.5",
    "usage": {
        "prompt_tokens": 10,
        "total_tokens": 10
    }
}
```

## 🗄️ 数据库兼容性

系统会根据选择的模型自动调整数据库表结构：

- **本地模型**: `vector(1024)` 
- **Google AI模型**: `vector(768)`

## 🧪 测试验证

### 1. 启动应用

```bash
./mvnw spring-boot:run
```

### 2. 检查日志

应用启动时会显示当前使用的模型：

```
🤖 正在初始化嵌入模型...
📋 模型类型: local
🏠 正在创建本地嵌入模型...
📋 本地模型配置信息:
   - 服务地址: http://localhost:8000
   - 模型名称: bge-large-en-v1.5
   - 向量维度: 1024
✅ 本地嵌入模型初始化完成
```

### 3. 运行测试

```bash
# 测试嵌入模型
./mvnw test -Dtest=EmbeddingModelTest

# 测试 RAG 服务  
./mvnw test -Dtest=RagServiceTest

# 测试向量存储
./mvnw test -Dtest=PostgresVectorStoreTest
```

### 4. API 测试

```bash
# 测试 RAG 问答
curl -X POST http://localhost:8081/api/ai/rag/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Java学习需要多长时间？"}'

# 测试知识库搜索
curl -X POST http://localhost:8081/api/ai/rag/search \
  -H "Content-Type: application/json" \
  -d '{"query": "Spring Boot特性", "maxResults": 3}'
```

## 🔄 切换模型

### 从 Google AI 切换到本地模型

1. 确保本地服务器运行在 8000 端口
2. 修改 `application.yml`:
   ```yaml
   embedding:
     model-type: local  # 改为 local
   ```
3. 重启应用

### 从本地模型切换到 Google AI 

1. 修改 `application.yml`:
   ```yaml
   embedding:
     model-type: google_ai  # 改为 google_ai
   ```
2. 重启应用

**注意**: 切换模型后，需要重新构建向量索引，因为向量维度不同。

## 🚨 故障排除

### 1. 本地服务器连接失败

**错误**: `连接本地嵌入模型失败`

**解决方案**:
- 检查本地服务器是否运行: `curl http://localhost:8000/health`
- 检查防火墙设置
- 确认端口8000未被占用

### 2. 模型加载失败

**错误**: `模型加载失败`

**解决方案**:
- 检查网络连接（首次需要下载模型）
- 确保有足够的内存（推荐8GB以上）
- 检查 Hugging Face 访问权限

### 3. 向量维度不匹配

**错误**: `vector dimension mismatch`

**解决方案**:
- 确认配置文件中的维度设置正确
- 清空数据库重新初始化:
  ```sql
  DROP TABLE IF EXISTS embeddings;
  ```
- 重启应用

## 📈 性能优化建议

### 1. 本地模型优化

- 使用GPU加速: `pip install torch[cuda]`
- 调整批处理大小
- 使用模型量化版本

### 2. 数据库优化

- 调整 `lists` 参数优化索引性能
- 根据数据量调整相似度阈值
- 定期更新表统计信息

## 🔮 未来计划

- [ ] 支持更多本地模型 (text2vec-base-chinese等)
- [ ] 模型热切换功能
- [ ] 向量缓存机制
- [ ] 模型性能监控

## 🤝 贡献

欢迎提交 Issues 和 Pull Requests 来完善本地嵌入模型功能！ 

 source venv/bin/activate && uvicorn embedding_server:app --host 0.0.0.0 --port 9000